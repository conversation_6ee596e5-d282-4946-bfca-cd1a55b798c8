<?php $__env->startSection('content'); ?>
<title><?php echo e(app()->getLocale() == 'ar' ? 'المعرض' : 'Gallery'); ?></title>

<!--==============================
    Breadcumb
============================== -->
<div class="breadcumb-wrapper" style="background-image: url('<?php echo e(asset('Front/assets/img/bg/breadcrumb-bg.png')); ?>');">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="breadcumb-content">
                    <h1 class="breadcumb-title"><?php echo e(app()->getLocale() == 'ar' ? 'المعرض' : 'Gallery'); ?></h1>
                    <ul class="breadcumb-menu">
                        <li><a href="<?php echo e(url('/')); ?>"><?php echo e(app()->getLocale() == 'ar' ? 'الرئيسية' : 'Home'); ?></a></li>
                        <li class="active"><?php echo e(app()->getLocale() == 'ar' ? 'المعرض' : 'Gallery'); ?></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!--==============================
    Gallery Area
==============================-->
<div class="gallery-area space">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-title text-center">
                    <h2 class="sec-title"><?php echo e(app()->getLocale() == 'ar' ? 'معرض الصور' : 'Photo Gallery'); ?></h2>
                    <p class="sec-text"><?php echo e(app()->getLocale() == 'ar' ? 'استكشف مجموعة من أفضل صورنا' : 'Explore our collection of finest images'); ?></p>
                </div>
            </div>
        </div>

        <?php if($Gallery && is_countable($Gallery) && count($Gallery) > 0): ?>
        <div class="row gallery-grid" id="gallery-container">
            <?php $__currentLoopData = $Gallery; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-lg-4 col-md-6 col-sm-12 gallery-item" data-category="<?php echo e($item->Category ?? 'all'); ?>">
                <div class="gallery-card">
                    <div class="gallery-img">
                        <img src="<?php echo e(asset($item->Image)); ?>" alt="<?php echo e(app()->getLocale() == 'ar' ? ($item->Arabic_Title ?? $item->Arabic_Name) : ($item->English_Title ?? $item->English_Name)); ?>" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h4><?php echo e(app()->getLocale() == 'ar' ? ($item->Arabic_Title ?? $item->Arabic_Name) : ($item->English_Title ?? $item->English_Name)); ?></h4>
                                <?php if($item->Arabic_Desc || $item->English_Desc): ?>
                                <p class="gallery-desc"><?php echo e(app()->getLocale() == 'ar' ? $item->Arabic_Desc : $item->English_Desc); ?></p>
                                <?php endif; ?>
                                <div class="gallery-actions">
                                    <a href="<?php echo e(asset($item->Image)); ?>" class="gallery-btn" data-lightbox="gallery" data-title="<?php echo e(app()->getLocale() == 'ar' ? ($item->Arabic_Title ?? $item->Arabic_Name) : ($item->English_Title ?? $item->English_Name)); ?>">
                                        <i class="fas fa-search-plus"></i>
                                    </a>
                                    <a href="#" class="gallery-btn share-btn" data-image="<?php echo e(asset($item->Image)); ?>" data-title="<?php echo e(app()->getLocale() == 'ar' ? ($item->Arabic_Title ?? $item->Arabic_Name) : ($item->English_Title ?? $item->English_Name)); ?>">
                                        <i class="fas fa-share-alt"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="gallery-info">
                        <h5><?php echo e(app()->getLocale() == 'ar' ? ($item->Arabic_Title ?? $item->Arabic_Name) : ($item->English_Title ?? $item->English_Name)); ?></h5>
                        <?php if($item->Category): ?>
                        <span class="gallery-category"><?php echo e($item->Category); ?></span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Load More Button -->
        <?php if(count($Gallery) > 9): ?>
        <div class="row">
            <div class="col-12 text-center mt-4">
                <button class="btn btn-primary" id="load-more-btn">
                    <?php echo e(app()->getLocale() == 'ar' ? 'عرض المزيد' : 'Load More'); ?>

                </button>
            </div>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <div class="row">
            <div class="col-12">
                <div class="empty-gallery text-center">
                    <i class="fas fa-images fa-5x text-muted mb-3"></i>
                    <h3><?php echo e(app()->getLocale() == 'ar' ? 'لا توجد صور في المعرض' : 'No Images in Gallery'); ?></h3>
                    <p class="text-muted"><?php echo e(app()->getLocale() == 'ar' ? 'سيتم إضافة الصور قريباً' : 'Images will be added soon'); ?></p>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
.gallery-area {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.section-title {
    margin-bottom: 60px;
}

.sec-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 15px;
}

.sec-text {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.gallery-grid {
    margin-bottom: 40px;
}

.gallery-item {
    margin-bottom: 30px;
}

.gallery-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.gallery-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.gallery-img {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.gallery-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-card:hover .gallery-img img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-card:hover .gallery-overlay {
    opacity: 1;
}

.gallery-content {
    text-align: center;
    color: white;
}

.gallery-content h4 {
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.gallery-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.gallery-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    border: 2px solid white;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.gallery-btn:hover {
    background: white;
    color: #333;
    text-decoration: none;
}

.gallery-info {
    padding: 20px;
    text-align: center;
}

.gallery-info h5 {
    margin-bottom: 10px;
    color: #333;
    font-weight: 600;
}

.gallery-category {
    background: #007bff;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.empty-gallery {
    padding: 100px 0;
}

.empty-gallery i {
    opacity: 0.3;
}

#load-more-btn {
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .gallery-item {
        margin-bottom: 20px;
    }

    .gallery-img {
        height: 200px;
    }

    .sec-title {
        font-size: 2rem;
    }
}

/* Lightbox Styles */
.lightbox {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
}

.lightbox-content {
    position: relative;
    margin: auto;
    padding: 20px;
    width: 90%;
    max-width: 800px;
    top: 50%;
    transform: translateY(-50%);
}

.lightbox img {
    width: 100%;
    height: auto;
    border-radius: 10px;
}

.lightbox-close {
    position: absolute;
    top: 10px;
    right: 25px;
    color: white;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
}

.lightbox-close:hover {
    color: #ccc;
}
</style>

<!-- Include Lightbox CSS and JS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css">

<script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize gallery
    let visibleItems = 9;
    let totalItems = $('.gallery-item').length;

    // Hide items beyond the initial visible count
    if (totalItems > visibleItems) {
        $('.gallery-item').slice(visibleItems).hide();
    } else {
        $('#load-more-btn').hide();
    }

    // Load more functionality
    $('#load-more-btn').click(function() {
        let hiddenItems = $('.gallery-item:hidden');
        let itemsToShow = hiddenItems.slice(0, 6);

        itemsToShow.fadeIn(500);

        if (hiddenItems.length <= 6) {
            $(this).fadeOut();
        }
    });

    // Share functionality
    $('.share-btn').click(function(e) {
        e.preventDefault();
        let imageUrl = $(this).data('image');
        let title = $(this).data('title');

        if (navigator.share) {
            navigator.share({
                title: title,
                url: imageUrl
            });
        } else {
            // Fallback for browsers that don't support Web Share API
            let shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(imageUrl)}`;
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }
    });

    // Lightbox configuration
    lightbox.option({
        'resizeDuration': 200,
        'wrapAround': true,
        'albumLabel': "<?php echo e(app()->getLocale() == 'ar' ? 'صورة %1 من %2' : 'Image %1 of %2'); ?>"
    });

    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        let imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    let img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(function(img) {
            imageObserver.observe(img);
        });
    }
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/site/Gallery.blade.php ENDPATH**/ ?>