<?php
use App\Models\CompanyData;
$Def = CompanyData::orderBy('id', 'desc')->first();
?>
<style>
.page-logo {
    width: 100% !important;
}
</style>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>
        <?php echo e(trans('admin.Login')); ?>

    </title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN" crossorigin="anonymous"></script>
    <meta name="description" content="Login">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=no, minimal-ui">
    <!-- Call App Mode on ios devices -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <!-- Remove Tap Highlight on Windows Phone IE -->
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />
    <!-- Place favicon.ico in the root directory -->
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(URL::to($Def->Icon)); ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(URL::to($Def->Icon)); ?>">
    <link rel="mask-icon" href="<?php echo e(URL::to($Def->Icon)); ?>" color="#5bbad5">
    <?php
    if (!function_exists('direction')) {
        function direction() {
            if (session()->has('lang')) {
                if (session('lang') == 'ar') {
                    return 'rtl';
                } else {
                    return 'ltr';
                }
            } else {
                return 'rtl';
            }
        }
    }
    ?>
    <?php if(direction() == 'ltr'): ?>
    <link id="vendorsbundle" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/vendors.bundle.css')); ?>">
    <link id="appbundle" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/app.bundle.css')); ?>">
    <link id="mytheme" rel="stylesheet" media="screen, print" href="#">
    <link id="myskin" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/skins/skin-master.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/page-login-alt.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/style.css')); ?>">
    <?php else: ?>
    <!-- base css -->
    <link id="vendorsbundle" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/vendors.bundle.css')); ?>">
    <link id="appbundle" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/app.bundle.css')); ?>">
    <link id="mytheme" rel="stylesheet" media="screen, print" href="#">
    <link id="myskin" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/skins/skin-master.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/page-login-alt.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/style.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/style-ar.css')); ?>">
    <?php endif; ?>
</head>
<body>
    <!-- DOC: script to save and load page settings -->
    <script>
        /**
         * This script should be placed right after the body tag for fast execution
         * Note: the script is written in pure javascript and does not depend on third-party library
         **/
        var classHolder = document.getElementsByTagName("BODY")[0],
            /**
             * Load from localstorage
             **/
            themeSettings = (localStorage.getItem('themeSettings')) ? JSON.parse(localStorage.getItem('themeSettings')) : {},
            themeURL = themeSettings.themeURL || '',
            themeOptions = themeSettings.themeOptions || '';

        /**
         * Load theme options
         **/
        if (themeSettings.themeOptions) {
            classHolder.className = themeSettings.themeOptions;
            console.log("%c✔ Theme settings loaded", "color: #148f32");
        } else {
            console.log("%c✔ Heads up! Theme settings is empty or does not exist, loading default settings...", "color: #ed1c24");
        }

        if (themeSettings.themeURL && !document.getElementById('mytheme')) {
            var cssfile = document.createElement('link');
            cssfile.id = 'mytheme';
            cssfile.rel = 'stylesheet';
            cssfile.href = themeURL;
            document.getElementsByTagName('head')[0].appendChild(cssfile);
        } else if (themeSettings.themeURL && document.getElementById('mytheme')) {
            document.getElementById('mytheme').href = themeSettings.themeURL;
        }

        /**
         * Save to localstorage
         **/
        var saveSettings = function () {
            themeSettings.themeOptions = String(classHolder.className).split(/[^\w-]+/).filter(function (item) {
                return /^(nav|header|footer|mod|display)-/i.test(item);
            }).join(' ');
            if (document.getElementById('mytheme')) {
                themeSettings.themeURL = document.getElementById('mytheme').getAttribute("href");
            };
            localStorage.setItem('themeSettings', JSON.stringify(themeSettings));
        };

        /**
         * Reset settings
         **/
        var resetSettings = function () {
            localStorage.setItem("themeSettings", "");
        };
    </script>

    <div class="background-image">
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 box-form pe-5">
                <div class="page-logo text-center">
                    <!-- Logo -->
                    <img src="https://res.cloudinary.com/dy6e9yvs9/image/upload/v1745012170/ost_erp_gglhqk.png" alt="OST ERP" aria-roledescription="logo">
                    <span class="page-logo-text mr-1">
                        <?php if(!empty($Def->Name)): ?>
                            <?php echo e(app()->getLocale() == 'ar' ? $Def->Name : $Def->NameEn); ?>

                        <?php else: ?>
                            <?php echo e(trans('admin.Ost')); ?>

                        <?php endif; ?>
                    </span>
                </div>
                <div class="card p-4 border-top-left-radius-0 border-top-right-radius-0">
                    <span id="ex"><?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                    <form action="<?php echo e(url('Login')); ?>" method="post" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>

                        <?php echo view('honeypot::honeypotFormFields'); ?>
                        <div class="form-group">
                            <input type="email" name="email" id="username" class="form-control" placeholder="<?php echo e(trans('admin.Email')); ?>" value="<?php echo e(old('email')); ?>" required>
                        </div>
                        <div class="form-group">
                            <input type="password" name="password" id="password" class="form-control" placeholder="<?php echo e(trans('admin.Password')); ?>" value="<?php echo e(old('password')); ?>" required>
                        </div>
                        <div class="form-group text-left">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="rememberme" id="rememberme">
                                <label class="custom-control-label" for="rememberme"><?php echo e(trans('admin.RmemberMe')); ?></label>
                            </div>
                        </div>
                        <button style="color: black;" type="submit" class="btn btn-default"><?php echo e(trans('admin.Login')); ?></button>
                    </form>
                </div>
                <div class="blankpage-footer text-center">
                    <a href="<?php echo e(url('forgotpassword')); ?>"><strong style="color: black;"><?php echo e(trans('admin.ForgotPassword')); ?></strong></a>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Modern Admin Login Styling */
        body {
            background: linear-gradient(135deg, #5c4b2d 0%, #4a3d24 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .background-image {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .box-form {
            width: 100%;
            max-width: 450px;
            margin: 0 auto;
        }

        /* Logo Section */
        .page-logo {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px 20px 0 0;
            padding: 30px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-logo img {
            max-width: 120px;
            height: auto;
            margin-bottom: 15px;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        .page-logo-text {
            display: block;
            color: white;
            font-size: 24px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            margin: 0;
        }

        /* Card Styling */
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0 0 20px 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            padding: 40px 30px !important;
            margin-top: 0;
        }

        /* Form Groups */
        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        /* Input Styling */
        .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .form-control:focus {
            border-color: #5c4b2d;
            box-shadow: 0 0 0 3px rgba(92, 75, 45, 0.1);
            background: white;
            outline: none;
        }

        .form-control::placeholder {
            color: #999;
            font-weight: 400;
        }

        /* Checkbox Styling */
        .custom-control-input:checked ~ .custom-control-label::before {
            background-color: #5c4b2d;
            border-color: #5c4b2d;
        }

        .custom-control-label {
            color: #666;
            font-weight: 500;
            cursor: pointer;
        }

        .custom-control-label::before {
            border-radius: 6px;
            border: 2px solid #ddd;
        }

        /* Button Styling */
        .btn-default {
            background: linear-gradient(135deg, #5c4b2d 0%, #4a3d24 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            color: white !important;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(92, 75, 45, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-default:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(92, 75, 45, 0.4);
            background: linear-gradient(135deg, #4a3d24 0%, #5c4b2d 100%);
        }

        .btn-default:active {
            transform: translateY(0);
        }

        /* Footer Link */
        .blankpage-footer {
            margin-top: 25px;
            padding: 20px;
        }

        .blankpage-footer a {
            color: white !important;
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .blankpage-footer a:hover {
            color: #f0f0f0 !important;
            text-decoration: underline;
        }

        /* Alert Messages */
        #ex .alert {
            border-radius: 12px;
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .background-image {
                padding: 15px;
            }

            .box-form {
                max-width: 100%;
            }

            .card {
                padding: 30px 20px !important;
            }

            .page-logo {
                padding: 25px 15px;
            }

            .page-logo-text {
                font-size: 20px;
            }

            .page-logo img {
                max-width: 100px;
            }
        }

        @media (max-width: 480px) {
            .card {
                padding: 25px 15px !important;
            }

            .form-control {
                padding: 12px 15px;
                font-size: 14px;
            }

            .btn-default {
                padding: 12px 25px;
                font-size: 14px;
            }

            .page-logo-text {
                font-size: 18px;
            }
        }

        /* Loading Animation */
        .btn-default:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        /* Focus Indicators for Accessibility */
        .form-control:focus,
        .btn-default:focus,
        .custom-control-input:focus ~ .custom-control-label::before {
            outline: 2px solid #5c4b2d;
            outline-offset: 2px;
        }
    </style>

    <!-- Page related scripts -->
    <script src="<?php echo e(asset('Admin/js/vendors.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/app.bundle.js')); ?>"></script>
    <script>
        $(document).ready(function () {
            setTimeout(function () { $("#ex").hide(); }, 6000);
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/admin/Login.blade.php ENDPATH**/ ?>