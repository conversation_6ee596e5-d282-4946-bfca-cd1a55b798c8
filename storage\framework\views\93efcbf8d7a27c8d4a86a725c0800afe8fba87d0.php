<?php $__env->startSection('content'); ?>
<?php
use App\Models\ItemsGroups;
$Groups=ItemsGroups::all();
?>
  <title><?php echo e(trans('admin.Items_Groups')); ?></title>

   <main id="js-page-content" role="main" class="page-content">
                        <ol class="breadcrumb page-breadcrumb">
                            <li class="breadcrumb-item"><a href="index.html"><?php echo e(trans('admin.Stores')); ?></a></li>  
                            <li class="breadcrumb-item active"><?php echo e(trans('admin.Items_Groups')); ?></li>
                            <li class="position-absolute pos-top pos-right d-none d-sm-block"><span class="js-get-date"></span></li>
                        </ol>
                        
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه مجموعه الاصناف')): ?>     
                        <div class="row">
                            <div class="col-lg-12">
                                <div id="panel-2" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                            <span class="fw-300"><i><?php echo e(trans('admin.AddNew')); ?></i></span>
                                        </h2>
                                    </div>
                                    <div class="panel-container show">         
                                        <div class="panel-content">
                  <form class="form-row" action="<?php echo e(url('AddItems_Groups')); ?>" method="post" enctype="multipart/form-data">
                                                <?php echo csrf_field(); ?>

                       <?php echo view('honeypot::honeypotFormFields'); ?>
                                                <div class="form-group col-md-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Arabic_Name')); ?></label>
                    <input type="text" name="Name" value="<?php echo e(old('Name')); ?>" id="simpleinput" class="form-control" required>
                                                </div>           
                      
                      <div class="form-group col-md-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.English_Name')); ?></label>
                    <input type="text" name="NameEn" value="<?php echo e(old('NameEn')); ?>" id="simpleinput" class="form-control" >
                                                </div>
                
                                         <div class="form-group col-md-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.HighestDiscountRate')); ?></label>
 <input type="number" step="any" name="Discount" value="<?php echo e(old('Discount')); ?>" id="simpleinput" class="form-control">
                                                </div>
                      
                      
                                                <div class="form-group col-md-2">
                                                    <label class="form-label" for=""> <?php echo e(trans('admin.Group')); ?> </label>
                           <select  class="select2 form-control w-100"  name="Parent">
                                      <option value=""><?php echo e(trans('admin.Group')); ?></option>
                                              <?php $__currentLoopData = $parentss; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $par): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                 <option value="<?php echo e($par->id); ?>">
                               
                                <?php echo e(app()->getLocale() == 'ar' ?$par->Name :$par->NameEn); ?>

                               </option>
                                               <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                        
                      
                                           <div class="form-group col-md-2">
                                             <label class="form-label" for=""><?php echo e(trans('admin.Store_Show')); ?>  </label>
                             <select class="select2 form-control w-100" name="Store_Show"  >
                 <option value="0" ><?php echo e(trans('admin.NO')); ?></option>
                 <option value="1" ><?php echo e(trans('admin.Storee')); ?></option>
                 <option value="2" ><?php echo e(trans('admin.Price_List')); ?></option>
                 <option value="3" ><?php echo e(trans('admin.Both')); ?></option>
                                                            </select>
                                                        </div>
                      
                      
                                     <div class="form-group col-md-2">
                                             <label class="form-label" for=""><?php echo e(trans('admin.Sales_Show')); ?>  </label>
                             <select class="select2 form-control w-100" name="Sales_Show"  >
                 <option value="0" ><?php echo e(trans('admin.NO')); ?></option>
                 <option value="1" ><?php echo e(trans('admin.Yes')); ?></option>
                                                            </select>
                                                        </div>
                      

                      
                      
                                                <div class="form-group col-md-2">
                                     <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Arrange')); ?></label>
                          <input type="number" name="Arrange"  class="form-control" value="<?php echo e(old('Arrange')); ?>">
                                                </div>        
                      
                      
                      <div class="form-group col-md-2">
                                     <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Printer')); ?></label>
                          <input type="text" name="Printer"  class="form-control" value="<?php echo e(old('Printer')); ?>">
                                                </div>       
                      
                      
                      <div class="form-group col-md-2">
                                     <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Notes')); ?></label>
                          <input type="text" name="Note"  class="form-control" value="<?php echo e(old('Note')); ?>">
                                                </div>
                                                
                                                      <div class="form-group col-md-2">
                                     <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Image')); ?></label>
                          <input type="file" name="Image" id="" class="form-control" >
                                                </div>            
                                                
            
                                                <div class="form-group col-md-2 buttons mt-4">
                              <button type="submit" class="btn btn-primary"> <i class="fal fa-folder"></i> <?php echo e(trans('admin.Save')); ?></button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
     <?php endif; ?>  
                        <div class="row">
                            <div class="col-lg-12">
                                <div id="panel-1" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                         <span class="fw-300"><i><?php echo e(trans('admin.Items_Groups')); ?></i></span>
                                        </h2>
                  
                                    </div>
                                    <div class="panel-container show">
                                     <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>          
                                        <div class="panel-content">
                                            <div class="tree">
                                                <ul>
                                                    <?php $__currentLoopData = $parents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $par): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li>
                                 <span><i class="fal fa-folder"></i> <?php echo e(app()->getLocale() == 'ar' ?$par->Name :$par->NameEn); ?> (<?php echo e($par->Discount); ?>)</span>
                                                         
                                                       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حذف مجموعه الاصناف')): ?>         
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-center<?php echo e($par->id); ?>"><i class="fal fa-trash-alt"></i></button>
                                                        <?php endif; ?>
                                                       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تعديل مجموعه الاصناف')): ?>         
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-lg-edit<?php echo e($par->id); ?>"><i class="fal fa-edit"></i></button>
                                                        <?php endif; ?>
                                                        
                                         <?php    $Childs=ItemsGroups::where('Parent',$par->id)->get();   ?>
                                                        
                                                <?php echo $__env->make('admin.Stores.SubGroups',['Childs' => $Childs], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
 
                                                    </li>
                                                    
                                               
                                     <!-- Modal Edit-->
                        <div class="modal fade" id="default-example-modal-lg-edit<?php echo e($par->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title"><?php echo e(trans('admin.Edit')); ?>  </h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                   <form action="<?php echo e(url('EditItems_Groups/'.$par->id)); ?>" method="post" enctype="multipart/form-data">
                                        <?php echo csrf_field(); ?>   
                                <input type="hidden" name="Images" value="<?php echo e($par->Image); ?>">            
                                <input type="hidden" name="Code" value="<?php echo e($par->Code); ?>">            
                                <input type="hidden" name="Type" value="<?php echo e($par->Type); ?>">            
                            
                                            
                                            <div class="form-row">
                                                <div class="form-group col-lg-6">
                                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Arabic_Name')); ?> </label>
                     <input type="text" name="Name" value="<?php echo e($par->Name); ?>" id="simpleinput" class="form-control" required>
                                                </div>          
                                                
                                                <div class="form-group col-lg-6">
                                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.English_Name')); ?> </label>
                     <input type="text" name="NameEn" value="<?php echo e($par->NameEn); ?>" id="simpleinput" class="form-control" required>
                                                </div>
                                         
                                                        <div class="form-group col-md-6">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Discount')); ?></label>
         <input type="number" step="any" name="Discount" value="<?php echo e($par->Discount); ?>" id="simpleinput" class="form-control">
                                                </div>            
                                                
                                                <div class="form-group col-md-12">
                              <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Arrange')); ?></label>
                    <input type="number" name="Arrange" id="" class="form-control" value="<?php echo e($par->Arrange); ?>">
                                                </div>                   <div class="form-group col-md-12">
                              <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Printer')); ?></label>
                    <input type="text" name="Printer" id="" class="form-control" value="<?php echo e($par->Printer); ?>">
                                                </div>
                                       <div class="form-group col-md-12">
                              <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Notes')); ?></label>
                    <input type="text" name="Note" id="simpleinput" class="form-control" value="<?php echo e($par->Note); ?>">
                                                </div>

                                                
                                                
                                                           <div class="form-group col-md-12" >
                                             <label class="form-label" for=""><?php echo e(trans('admin.Store_Show')); ?>  </label>
                             <select class="select2 form-control w-100" name="Store_Show"  >
                 <option value="0"  <?php if($par->Store_Show == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.NO')); ?></option>
                 <option value="1"  <?php if($par->Store_Show == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Storee')); ?></option>
                 <option value="2"  <?php if($par->Store_Show == 2): ?> selected <?php endif; ?>><?php echo e(trans('admin.Price_List')); ?></option>
                 <option value="3"  <?php if($par->Store_Show == 3): ?> selected <?php endif; ?>><?php echo e(trans('admin.Both')); ?></option>
                                                            </select>
                                                        </div>
                      
                      
                                     <div class="form-group col-md-12">
                                             <label class="form-label" for=""><?php echo e(trans('admin.Sales_Show')); ?>  </label>
                             <select class="select2 form-control w-100" name="Sales_Show"  >
                 <option value="0"  <?php if($par->Sales_Show == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.NO')); ?></option>
                 <option value="1"  <?php if($par->Sales_Show == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                                                            </select>
                                                        </div>
                          
                                                
                                                                        <div class="form-group col-md-12">
                                                    <label class="form-label" for=""> <?php echo e(trans('admin.Group')); ?> </label>
                           <select  class="select2 form-control w-100"  name="Parent">
                                      <option value=""><?php echo e(trans('admin.Group')); ?></option>
                                              <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grop): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                 <option value="<?php echo e($grop->id); ?>" <?php if($grop->id == $par->Parent): ?> selected <?php endif; ?>>
                               
                                <?php echo e(app()->getLocale() == 'ar' ?$grop->Name :$grop->NameEn); ?>

                               </option>
                                               <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                                
                                                
                                                
                                                 <div class="form-group col-md-12">
                                     <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Image')); ?></label>
                          <input type="file" name="Imager">
                                                </div>         
                                           <img src="<?php echo e(URL::to($par->Image)); ?>" width="20%" height="20%">         
                                            </div>
                                  <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?> </button>
                                  <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.SaveChanges')); ?> </button>
                                    </div>                    
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>                    
                                                    
                               <!-- Modal Delete -->
                        <div class="modal fade" id="default-example-modal-center<?php echo e($par->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h4 class="modal-title">
                                            
                                            <?php echo e(trans('admin.RUSWDT')); ?> <strong><?php echo e(app()->getLocale() == 'ar' ?$par->Name :$par->NameEn); ?></strong>   
                                        </h4>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                        </button>
                                    </div>
                                    
                                    <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.No')); ?></button>
                        <a href="<?php echo e(url('DeleteItems_Groups/'.$par->id)); ?>" class="btn btn-primary"><?php echo e(trans('admin.Yes')); ?></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                                   
                                                    
                                                   <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
       
                   
                 
                    </main>


<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>
     <!-- DEMO related CSS below -->
        <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/fa-brands.css')); ?>">
        <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/miscellaneous/treeview/treeview.css')); ?>">
        <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">


    <script src="<?php echo e(asset('Admin/js/miscellaneous/treeview/treeview.js')); ?>"></script>
        <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
        <script>
            document.addEventListener("DOMContentLoaded", function()
            {
                // Handler when the DOM is fully loaded
            });

        </script>
      <!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }

    
        
  $('#account').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllMainAccounts',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };
            
            	console.log(data);
            
        },
        data: function (params) {  
          var query = {
            search: params.term
          };
          if (params.term == "*") query.items = [];
          return { json: JSON.stringify( query ) }
        }
    }
  });

    
$('#account').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
           
     

             
            });
        });



    </script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/admin/Stores/ItemsGroups.blade.php ENDPATH**/ ?>