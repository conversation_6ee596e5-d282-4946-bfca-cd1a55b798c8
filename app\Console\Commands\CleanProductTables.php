<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CleanProductTables extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:clean {--confirm : Skip confirmation prompt}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean all product-related tables in the database';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if (!$this->option('confirm')) {
            if (!$this->confirm('This will delete ALL product data and related records. Are you sure?')) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        $this->info('Starting product tables cleanup...');

        try {
            DB::beginTransaction();

            // List of tables to clean (in order to avoid foreign key constraints)
            $tablesToClean = [
                // Child tables first (tables that reference products)
                'assembly_products',
                'fifo_qties',
                'product_moves',
                'product_moves_filters',
                'product_moves_filter_twos',
                'product_units',
                'products_purchases',
                'products_qties',
                'products_start_periods',
                'products_stores',
                'sub_images',
                'ticket_products',
                
                // Main products table last
                'products',
            ];

            $cleanedTables = [];
            $totalRecordsDeleted = 0;

            foreach ($tablesToClean as $table) {
                if (Schema::hasTable($table)) {
                    $recordCount = DB::table($table)->count();
                    
                    if ($recordCount > 0) {
                        DB::table($table)->truncate();
                        $cleanedTables[] = $table;
                        $totalRecordsDeleted += $recordCount;
                        $this->info("✓ Cleaned table: {$table} ({$recordCount} records deleted)");
                    } else {
                        $this->info("- Table {$table} was already empty");
                    }
                } else {
                    $this->warn("! Table {$table} does not exist");
                }
            }

            DB::commit();

            $this->info('');
            $this->info('=== CLEANUP SUMMARY ===');
            $this->info("Tables cleaned: " . count($cleanedTables));
            $this->info("Total records deleted: {$totalRecordsDeleted}");
            $this->info('');
            
            if (!empty($cleanedTables)) {
                $this->info('Cleaned tables:');
                foreach ($cleanedTables as $table) {
                    $this->info("  - {$table}");
                }
            }

            $this->info('');
            $this->info('✅ Product tables cleanup completed successfully!');

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('❌ Error during cleanup: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
