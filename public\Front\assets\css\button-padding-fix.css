/* Product Card Button Padding Fix */
/* Minimal fix for the 2 floated buttons in product cards */

/* Fix padding for the action buttons */
.product-card .actions .btn,
.product-card .actions .btn.style2 {
    padding: 8px 12px !important;
    margin: 0 5px !important;
    min-width: 40px !important;
    min-height: 40px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Ensure proper spacing between the 2 buttons */
.product-card .actions {
    gap: 10px !important;
}

/* Fix any margin issues */
.product-card .actions > * {
    margin: 0 !important;
}

/* Ensure icons are properly centered */
.product-card .actions .btn i,
.product-card .actions .btn.style2 i {
    margin: 0 !important;
    line-height: 1 !important;
}

/* Cart Page Button Fixes */
/* Fix Continue Shopping button - make text white always, not just on hover */
.continue-shopping {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
    border: 2px solid #28a745 !important;
}

.continue-shopping:hover {
    background: linear-gradient(135deg, #20c997 0%, #28a745 100%) !important;
    color: white !important;
    border-color: #20c997 !important;
    text-decoration: none !important;
}
