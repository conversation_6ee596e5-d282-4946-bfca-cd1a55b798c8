<?php $__env->startSection('content'); ?>


  <title><?php echo e(trans('admin.Edit_Items')); ?></title>
  <style>
      .control-img{
          height:110px;
          max-width:100%;
      }
      .text{
    font-size:15px;
    color:#d4a1cc;
    animation: first 2s linear 2s infinite alternate;
}
@keyframes  first{from{font-size:15px;color:#d4a1cc}
to {font-size:18px;color:orange}}
  </style>

   <main id="js-page-content" role="main" class="page-content">
       <form action="<?php echo e(url('PostEditProduct/'.$item->id)); ?>" method="post" enctype="multipart/form-data"> 
                              <?php echo csrf_field(); ?>   
            <?php echo view('honeypot::honeypotFormFields'); ?>
        <input type="hidden" name="Images" value="<?php echo e($item->Image); ?>">   
        <input type="hidden" name="Images2" value="<?php echo e($item->Image2); ?>">   
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Stores')); ?></a></li>
                        <li class="breadcrumb-item active"><?php echo e(trans('admin.Edit_Items')); ?></li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
                   
                    <div class="row">
                        <div class="col-xl-12">
                            <div  class="panel">
                                <div class="panel-container show">
                                    <div class="panel-content">
                                        <div class="panel-container show">
                                             <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>  
                                            <div class="panel-content">
                                                <ul class="nav nav-tabs" role="tablist">
                                                    <li class="nav-item">
                                                        <a class="nav-link active" data-toggle="tab" href="#tab_borders_icons-1" role="tab"><i class="fal fa-home mr-1"></i>    <?php echo e(trans('admin.Main_Data')); ?></a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-2" role="tab"><i class="fal fa-user mr-1"></i>    <?php echo e(trans('admin.Sub_Data')); ?></a>
                                                    </li>
                                                    
                                                                   <li class="nav-item">
                                 <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-3" role="tab"><i class="fal fa-user mr-1"></i>    <?php echo e(trans('admin.Additions')); ?></a>
                              </li>
                                                   
                                                </ul>
                       
                                                <div class="tab-content border border-top-0 p-3">
                                  <div class="tab-pane fade show active" id="tab_borders_icons-1" role="tabpanel">
                                                       <div class="form-row">
  
                                                        <div class="form-group col-md-2">
                                                            <label class="form-label" for=""> 
                                                    <?php echo e(trans('admin.Product_Type')); ?>    
                                                        </label><span class="strick">*</span>
                        <select class="select2 form-control w-100" id="P_Type" onchange="Assembly()" disabled>
                                         <option value=""><?php echo e(trans('admin.Product_Type')); ?> </option>
   <option value="Completed" <?php if($item->P_Type == 'Completed'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Completed')); ?> </option>
     <option value="Raw" <?php if($item->P_Type == 'Raw'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Raw')); ?> </option>
     <option value="Service" <?php if($item->P_Type == 'Service'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Service')); ?></option>
    <option value="Assembly" <?php if($item->P_Type == 'Assembly'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Assembly')); ?></option>
     <option value="Industrial" <?php if($item->P_Type == 'Industrial'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Industrial')); ?></option>
<option value="Single_Variable" <?php if($item->P_Type == 'Single_Variable'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Single_Variable')); ?></option>
 <option value="Duble_Variable" <?php if($item->P_Type == 'Duble_Variable'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Duble_Variable')); ?></option>
<option value="Serial" <?php if($item->P_Type == 'Serial'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Serial')); ?></option>
<option value="Subscribe" <?php if($item->P_Type == 'Subscribe'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Subscribe')); ?></option>
<option value="Petroll" <?php if($item->P_Type == 'Petroll'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Petroll')); ?></option>
<option value="Variable_Aggregate" <?php if($item->P_Type == 'Variable_Aggregate'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Variable_Aggregate')); ?></option>
<option value="Additions" <?php if($item->P_Type == 'Additions'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Additions')); ?></option>
                                                            </select>
                        <input type="hidden"  name="P_Type" value="<?php echo e($item->P_Type); ?>">                                    
                                                        </div>
                                                        <div class="form-group col-md-2">
                               <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Product_Ar_Name')); ?> 
                                                    </label><span class="strick">*</span>
     <input type="text" name="P_Ar_Name" value="<?php echo e($item->P_Ar_Name); ?>" placeholder="<?php echo e(trans('admin.Product_Ar_Name')); ?> "  class="form-control" required>
                                                        </div>
                                                        <div class="form-group col-md-2">
                             <label class="form-label" for="simpleinput">  <?php echo e(trans('admin.Product_En_Name')); ?></label>
 <input type="text" name="P_En_Name" value="<?php echo e($item->P_En_Name); ?>" placeholder="<?php echo e(trans('admin.Product_En_Name')); ?> "  class="form-control">
                                                        </div>
                                                        <div class="form-group col-md-2">
                                    <label class="form-label" for="">  <?php echo e(trans('admin.Brand')); ?> </label>
                                                            <select class="select2 form-control w-100" name="Brand">
                                                                <option value=""><?php echo e(trans('admin.Brand')); ?></option>
                                                                <?php $__currentLoopData = $Brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
             <option value="<?php echo e($brand->id); ?>" <?php if($item->Brand == $brand->id): ?> selected <?php endif; ?>>
                                                                
                                                       <?php echo e(app()->getLocale() == 'ar' ?$brand->Name :$brand->NameEn); ?>                   
                                                                </option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                                        </div>
                                                        <div class="form-group col-md-2">
                  <label class="form-label" for="">  <?php echo e(trans('admin.Group')); ?>  </label><span class="strick">*</span>
                                                <select class="select2 form-control w-100" name="Group" required>
                                       <option value=""><?php echo e(trans('admin.Group')); ?></option>
                                                                <?php $__currentLoopData = $ItemsGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
     <option value="<?php echo e($group->id); ?>" <?php if($item->Group == $group->id): ?> selected <?php endif; ?>>
                                                    
                                      <?php echo e(app()->getLocale() == 'ar' ?$group->Name :$group->NameEn); ?>                     
                                                    </option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                                        </div>
                                                       
                                                   
                                          <div class="form-group col-md-2">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.If_Offer')); ?> </label>
                                       <select class="select2 form-control w-100" name="Offer">
                                          <option value=""><?php echo e(trans('admin.If_Offer')); ?></option>
                            <option value="1" <?php if($item->Offer == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                            <option value="0" <?php if($item->Offer == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                                       </select>
                                    </div> 
                                                          
                                                      <div class="form-group col-md-2">
                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.OfferPrice')); ?> </label>
                    <input type="number" step='any' class="form-control" name="OfferPrice" value="<?php echo e($item->OfferPrice); ?>">
                                    </div> 
                                                           
                                                           
                                                                                                                                         
                                                      <div class="form-group col-md-2">
                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Offer_Start_Date')); ?> </label>
                    <input type="date" step='any' class="form-control" name="Offer_Start_Date"  value="<?php echo e($item->Offer_Start_Date); ?>">
                                    </div>   
                                     
                                                                                 
                                                      <div class="form-group col-md-2">
                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Offer_End_Date')); ?> </label>
                    <input type="date" step='any' class="form-control" name="Offer_End_Date"  value="<?php echo e($item->Offer_End_Date); ?>">
                                    </div>   
                                     
                                     
                                     <div class="form-group col-md-2 mb-2">
      <label class="form-label customize-input" for="imgInp"><i class="fal fa-image"></i> <?php echo e(trans('admin.Image')); ?>  </label>
                                            <input type="file" name="Image">
                                                            
                                                            
                                        <?php if(!empty($item->Image)): ?>
                                     <img src="<?php echo e(URL::to($item->Image)); ?>" class="control-img">                       
                                        <?php endif; ?>                    
                                                        </div>
                                                 <div class="form-group col-md-2 mb-2">
      <label class="form-label customize-input" for="imgInp"><i class="fal fa-image"></i> <?php echo e(trans('admin.Image2')); ?>  </label>
                                            <input type="file" name="Image2">
            
                                        <?php if(!empty($item->Image2)): ?>
                                     <img src="<?php echo e(URL::to($item->Image2)); ?>" class="control-img">                       
                                        <?php endif; ?>                    
                                                        </div>
                                                           
                                  <div class="form-group col-md-12 mb-3" style="display: none" id="EndDate">
                                      <div class="row">
                                          <div class="col-md-6">
                          <label class="form-label" for="simpleinput">  <?php echo e(trans('admin.Sub_Cost')); ?></label>
 <input type="number" step="any" name="Sub_Cost" value="<?php echo e($item->Sub_Cost); ?>" placeholder="<?php echo e(trans('admin.Sub_Cost')); ?> "  class="form-control">
                              </div>
                               <div class="col-md-6">
               <label class="form-label" for="">  <?php echo e(trans('admin.Subscribe_Type')); ?>  </label>
                                                <select class="select2 form-control w-100" name="subscribe_type" >
                                       <option value=""><?php echo e(trans('admin.Subscribe_Type')); ?></option>
                                                                <?php $__currentLoopData = $SubscribeTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sub): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($sub->id); ?>" <?php if($item->subscribe_type == $sub->id): ?>  selected <?php endif; ?>>
                                            <?php echo e(app()->getLocale() == 'ar' ?$sub->Name :$sub->NameEn); ?>              
                                                    </option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
</div>
                              
                                                        </div>  
                                                        </div>   
                                                           
                                                    <!-- Assembly -->       
                                    <div id="HideAssembly" style="display: none">
                                                         <div class="form-group col-md-3 mb-3">
                                            <div class="input-items">
         <input type="text" id="search" class="form-control" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?> ">
                   <i class="fal fa-barcode-alt" style="margin-top: -25px;position: absolute;margin-right: 967px;"></i>
                                            </div>
                                         </div>  
                                        
                            
                     <table class="table table-bordered table-hover table-striped w-100 hide-products-table">
                                                        <thead>
                                                            <tr>
                                                                <th><?php echo e(trans('admin.Name')); ?></th>
                                                                <th><?php echo e(trans('admin.Units')); ?></th>
                                                                <th><?php echo e(trans('admin.Qty')); ?></th>
                                                                <th><?php echo e(trans('admin.Price')); ?> </th>
                                                                <th><?php echo e(trans('admin.Total')); ?></th>
                                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="data">
                                 
                                                          
                                                        </tbody>
                                                    </table>            
                                        
                        <table class="table table-bordered table-hover table-striped w-100 hide-products-table">
                                                        <thead>
                                                            <tr>
                                                                <th><?php echo e(trans('admin.Name')); ?></th>
                                                                <th><?php echo e(trans('admin.Barcode')); ?></th>
                                                                <th><?php echo e(trans('admin.Units')); ?></th>
                                                                <th><?php echo e(trans('admin.Qty')); ?></th>
                                                                <th><?php echo e(trans('admin.Price')); ?> </th>
                                                                <th><?php echo e(trans('admin.Total')); ?></th>
                                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="data-dt">
                                 <?php $__currentLoopData = $Assembls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $asi): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>                            
               <tr>
                   <td>
                       <input type='hidden' name='P_Ar_NameAssem[]' value='<?php echo e($asi->P_Ar_Name); ?>'>
                       <input type='hidden' name='P_En_NameAssem[]' value='<?php echo e($asi->P_En_Name); ?>'>
             
                       
                 <?php echo e(app()->getLocale() == 'ar' ?$asi->P_Ar_Name :$asi->P_En_Name); ?>        
                   </td>
                   <td><input type='hidden' name='P_CodeAssem[]' value='<?php echo e($asi->P_Code); ?>'><?php echo e($asi->P_Code); ?></td>
                   <td><input type='hidden' name='UnitAssem[]' value='<?php echo e($asi->Unit); ?>'>
                 <?php echo e(app()->getLocale() == 'ar' ?$asi->Unit()->first()->Name :$asi->Unit()->first()->NameEn); ?>    
                   </td>
                   <td><input type='hidden' name='QtyAssem[]' value='<?php echo e($asi->Qty); ?>'><?php echo e($asi->Qty); ?></td>
                   <td><input type='hidden' name='PriceAssem[]' value='<?php echo e($asi->Price); ?>'><?php echo e($asi->Price); ?></td>
                   <td><input type='hidden' name='TotalAssem[]' value='<?php echo e($asi->Total); ?>'><?php echo e($asi->Total); ?>

                    <input id='Product_IDInp<?php echo e($asi->Product); ?>' type='hidden' name='ProductAssem[]' value='<?php echo e($asi->Product); ?>'>
                   </td>
                   <td>  
                 <button id='DelAssem' type='button' class='btn btn-default'><i class='fal fa-trash'></i></button>
             
                   </td>
            </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                                                          
                                                          
                                                        </tbody>
                                                    </table>
                                 
                                                           </div>
                                                           
               
                                                        </div>           
                                         
                                   <!-- Vira One -->       
                                    <div id="HideViraOne" style="display: none" class="col-md-3 mb-3">
                                       <div class="form-group ">
                                          <label class="form-label" for=""><?php echo e(trans('admin.Virable')); ?> </label>
                                          <select class="select2 form-control w-100" id="VOne">
                                             <option value=""><?php echo e(trans('admin.Virable')); ?></option>
                                             <?php $__currentLoopData = $Virables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vir): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                       
                                              
                                             <option value="<?php echo e($vir->id); ?>" 
                                                     
                                                             <?php $__currentLoopData = $ProductsVira; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pv): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                                     <?php if($vir->id == $pv->V1()->first()->V_ID): ?>
                                                 selected
                                                     <?php endif; ?>
                                                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                                                        >
                                                    <?php echo e(app()->getLocale() == 'ar' ?$vir->Name :$vir->NameEn); ?> 
                                                 </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                          </select>
                                       </div>
                                       <div class="padding-tables" style="display: none">
                                          <table id="color" class="table table-bordered table-hover table-striped w-100">
                                             <thead class="bg-highlight">
                                                <tr>
                                                   <th><?php echo e(trans('admin.Virable')); ?></th>
                                                   <th><?php echo e(trans('admin.Cost')); ?></th>
                                                </tr>
                                             </thead>
                                             <tbody class="ViraOne">
                                             </tbody>
                                          </table>
                                       </div>
                                    </div>
                                    <!-- Vira Two -->       
                                    <div id="HideViraTwo" style="display: none" class="col-md-6 mb-6">
                                       <div class="form-row">
                                          <div class="form-group col-lg-6 ">
                                             <label class="form-label" for=""><?php echo e(trans('admin.Virable')); ?> </label>
                                             <select class="select2 form-control w-100" id="VTwo">
                                                <option value=""><?php echo e(trans('admin.Virable')); ?></option>
                                                <?php $__currentLoopData = $Virables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vir): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($vir->id); ?>"
                                                                <?php $__currentLoopData = $ProductsVira; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pv): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                                     <?php if($vir->id == $pv->V1()->first()->V_ID): ?>
                                                 selected
                                                     <?php endif; ?>
                                                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                                                        >
                                                    <?php echo e(app()->getLocale() == 'ar' ?$vir->Name :$vir->NameEn); ?> 
                                                 </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                             </select>
                                          </div>
                                           
                                          <div class="form-group col-lg-6 ">
                                             <label class="form-label" for=""><?php echo e(trans('admin.Virable')); ?> </label>
                                             <select class="select2 form-control w-100" id="VTwoo">
                                                <option value=""><?php echo e(trans('admin.Virable')); ?></option>
                                                <?php $__currentLoopData = $Virables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vir): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($vir->id); ?>"
                                                            <?php $__currentLoopData = $ProductsVira; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pv): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                                     <?php if(!empty($pv->V2()->first()->V_ID)): ?>
                                                     <?php if($vir->id == $pv->V2()->first()->V_ID): ?>
                                                 selected
                                                     <?php endif; ?>
                                                     <?php endif; ?>
                                                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>        
                                                        
                                                        >   <?php echo e(app()->getLocale() == 'ar' ?$vir->Name :$vir->NameEn); ?> </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                             </select>
                                          </div>
                                          <div class="alert alert-danger" id="AlertVira" style="display: none">
                                             <?php echo e(trans('admin.Can_not_Choice_Same_Virables')); ?>    
                                          </div>
                                       </div>
                                       <div style="display: none">
                                          <div class="padding-tables" id="TabV2">
                                             <table id="variables" class="table table-bordered table-hover">
                                             </table>
                                          </div>
                                       </div>
                                    </div>        
                                                       <div class="row">
                                                        <div class="col-xl-12">
                                                            <div id="panel-1" class="panel">
                                                                <div class="panel-hdr">
                                                                    <h2>
                                                            <?php echo e(trans('admin.Edit_Items')); ?>   
                                                                    </h2>
                                                                    <div class="panel-toolbar">
                                                                        <button class="btn btn-primary btn-sm" data-toggle="dropdown">Table
                                                                            Style</button>
                                                                        <div
                                                                            class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                                                            <button class="dropdown-item active" data-action="toggle"
                                                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                                                Table </button>
                                                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                                                data-target="#dt-basic-example"> Smaller Table </button>
                                                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                                                data-target="#dt-basic-example"> Table Dark </button>
                                                                            <button class="dropdown-item active" data-action="toggle"
                                                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                                                            </button>
                                                                            <button class="dropdown-item active" data-action="toggle"
                                                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                                                Stripped </button>
                                                                            <div class="dropdown-divider m-0"></div>
                                                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                                                <div class="dropdown-item">
                                                                                    tbody color
                                                                                </div>
                                                                                <div class="dropdown-menu no-transition-delay">
                                                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                                                        style="width: 15.9rem; padding: 0.5rem">
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg=""
                                                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                                                <div class="dropdown-item">
                                                                                    thead color
                                                                                </div>
                                                                                <div class="dropdown-menu no-transition-delay">
                                                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                                                        style="width: 15.9rem; padding: 0.5rem">
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                        <a href="javascript:void(0);" data-bg=""
                                                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                                                            style="margin:1px"></a>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                           <!-- Units -->
                                                                <div class="panel-container show">
                                                                    <div class="panel-content">
                                                      <span class="text"><?php echo e(trans('admin.ArabicCode')); ?></span>    
                                                      <div id="mobile-overflow">
                                                    <table class="table table-bordered table-hover table-striped w-100 th-width mobile-width table-color1 mt-2">
                                                                        <thead>
                                                                            <tr>
                                                                                <th><?php echo e(trans('admin.Unit')); ?></th>
                                                                                <th><?php echo e(trans('admin.Rate')); ?></th>
                                                                                <th><?php echo e(trans('admin.Barcode')); ?></th>
                                                                                <th><?php echo e(trans('admin.Price_One')); ?></th>
                                                                                <th><?php echo e(trans('admin.Price_Two')); ?></th>
                                                                                <th><?php echo e(trans('admin.Price_Three')); ?></th>
                                                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            <tr>
                                                                                <td>
                                                                                    <div class="form-group">
                                                                                      
                                                     <select class="select2 form-control w-100" id="unit" onchange="PLUS()">
                                                                  <option value=""><?php echo e(trans('admin.Unit')); ?></option>
                                                         <?php $__currentLoopData = $Units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $uni): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($uni->id); ?>">
                                                         <?php echo e(app()->getLocale() == 'ar' ?$uni->Name :$uni->NameEn); ?>      
                                                         </option>
                                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                                        </select>
                                                        <input type="hidden" id="UnitName" >                               
                                                        <input type="hidden" id="UnitID" >                               
                                                                                    </div>
                                                                                </td>
                                                                                <td>
                                                                                    <div class="form-group">
                     <input type="number" step="any" id="rate" class="form-control" onkeyup="PLUS()" onclick="PLUS()">
                                                                                    </div>
                                                                                </td>
                                                                                <td>
                                                                                    <div class="form-group" style="position:relative;" >
                                                      <input type="text" id="code" class="form-control" onkeyup="PLUS()" >
     <i class="fal fa-barcode" onclick="Genrte()" style="position: absolute;left:3px;top:13px;"></i>                
                                                                                    </div>
                                                                                </td>
                                                                                <td>
                                                                                    <div class="form-group">
                                                                                       
                  <input type="number" step="any" id="price1" class="form-control" onkeyup="PLUS()" onclick="PLUS()">
                                                                                    </div>
                                                                                </td>
                                                                                <td>
                                                                                    <div class="form-group">
                                                                                       
                  <input type="number" step="any" id="price2" class="form-control" onkeyup="PLUS()" onclick="PLUS()">
                                                                                    </div>
                                                                                </td>
                                                                                <td>
                                                                                    <div class="form-group">
            <input type="number" step="any" id="price3" class="form-control" onkeyup="PLUS()" onclick="PLUS()">
                                                                                    </div>
                                                                                </td>
                                                                                <td>
    <button type="button" onclick="InsertData()" class="btn btn-default" id="add-data" style="display: none"><i class="fal fa-plus"></i></button>
                                                                                </td>
                                                                               
                                                                            </tr>
                                                                        </tbody>
                                                                        </table>
                                                                        </div>
                                                                        <!-- datatable start -->
                                                                        <div id="mobile-overflow">
                          <table id="samble"   class="table table-bordered table-hover table-striped w-100 th-width mobile-width table-color2">
                                                                            <thead>
                                                                                <tr>
                                                                                 <th><?php echo e(trans('admin.Unit')); ?></th>
                                                                                <th><?php echo e(trans('admin.Rate')); ?></th>
                                                                                <th><?php echo e(trans('admin.Barcode')); ?></th>
                                                                                <th><?php echo e(trans('admin.Price_One')); ?></th>
                                                                                <th><?php echo e(trans('admin.Price_Two')); ?></th>
                                                                                <th><?php echo e(trans('admin.Price_Three')); ?></th>
                                                                                <th><?php echo e(trans('admin.Default_Unit')); ?></th>
                                                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody id="data-dt-first">
                                             <?php $__currentLoopData = $ProsUnit; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>                        
                                                                   <tr> 
                                                        <td>
                           
                                                            
                                  <?php echo e(app()->getLocale() == 'ar' ?$pu->Unit()->first()->Name :$pu->Unit()->first()->NameEn); ?>                            
                        <input type="hidden" name="Rate[]" value="<?php echo e($pu->Rate); ?>">
                        <input type="hidden" name="Barcode[]" value="<?php echo e($pu->Barcode); ?>">
                        <input type="hidden" name="Price[]" value="<?php echo e($pu->Price); ?>">
                        <input type="hidden" name="Price_Two[]" value="<?php echo e($pu->Price_Two); ?>">
                        <input type="hidden" name="Price_Three[]" value="<?php echo e($pu->Price_Three); ?>">
                        <input type="hidden" id="U<?php echo e($pu->Unit); ?>" name="Unit[]" value="<?php echo e($pu->Unit); ?>">
                                                    </td>
                                                        <td><?php echo e($pu->Rate); ?></td>
                                                        <td><?php echo e($pu->Barcode); ?></td>
                                                        <td><?php echo e($pu->Price); ?></td>
                                                        <td><?php echo e($pu->Price_Two); ?></td>
                                                        <td><?php echo e($pu->Price_Three); ?></td>
                                           
                                                                       <td>
                  <input type="radio" onclick="DEFAULT(<?php echo e($pu->Unit); ?>)"  name="DefaultUnit"  required <?php if($pu->Def == 1): ?> checked value="1" <?php else: ?> value="0" <?php endif; ?>>  
                                                                       
                <input type="hidden" class="default" id="def<?php echo e($pu->Unit); ?>" name="Def[]" <?php if($pu->Def == 1): ?> value="1" <?php else: ?> value="0" <?php endif; ?>>                    
                                                                       </td>
                                                        <td>
                                           <button id="Del" type="button" class="btn btn-default"><i class="fal fa-trash"></i></button>
                                                        </td>
                                                     </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>                            
                                                                            </tbody>
                                              
                                                                        </table>
                                                                        </div>
                                                                        <!-- datatable end -->
                                                                      
                         
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>  
                                                    </div>
                                              
                                                
                                      <div class="tab-pane fade" id="tab_borders_icons-2" role="tabpanel">
                                                       <div class="form-row">
                                                       
                                                        <div class="form-group col-md-2">
                                                            <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Maximum_Sales_Qty')); ?>  </label>
                                 <input type="number" name="Maximum_Sales_Qty" value="<?php echo e($item->Maximum_Sales_Qty); ?>" class="form-control">
                                                        </div>             
                                                        <div class="form-group col-md-2">
                                                            <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Minimum')); ?>  </label>
                                 <input type="number" name="Minimum" value="<?php echo e($item->Minimum); ?>" class="form-control">
                                                        </div>
                                                        <div class="form-group col-md-2">
                                                            <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Maximum')); ?>  </label>
                                <input type="number" name="Maximum" value="<?php echo e($item->Maximum); ?>" class="form-control">
                                                        </div>
                                                        <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Length')); ?>  </label>
                          <input type="number" step="any" name="Length" value="<?php echo e($item->Length); ?>" class="form-control">
                                                        </div>
                                                        <div class="form-group col-md-2">
                                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Width')); ?>   </label>
                          <input type="number" step="any" name="Width" value="<?php echo e($item->Width); ?>" class="form-control">
                                                        </div>
                                                        <div class="form-group col-md-2">
                                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Height')); ?>   </label>
                         <input type="number" step="any" name="Height" value="<?php echo e($item->Height); ?>" class="form-control">
                                                        </div>
                                                        <div class="form-group col-md-2">
                                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Weight')); ?>   </label>
                <input type="number" step="any" name="Weight" value="<?php echo e($item->Weight); ?>" class="form-control">
                                                        </div>                            
                                                           <div class="form-group col-md-2">
                                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Thickness')); ?>   </label>
                <input type="number" step="any" name="Thickness" value="<?php echo e($item->Thickness); ?>" class="form-control">
                                                        </div>
                                                        <div class="form-group col-md-2">
                                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Saller_Point')); ?>   </label>
                 <input type="number" step="any" name="Saller_Point" value="<?php echo e($item->Saller_Point); ?>" class="form-control">
                                                        </div>
                                                        <div class="form-group col-md-2">
                                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Customer_Point')); ?>  </label>
             <input type="number" step="any" name="Customer_Point" value="<?php echo e($item->Customer_Point); ?>" class="form-control">
                                                        </div>                                                   
                                                           
                                                           <div class="form-group col-md-2">
                                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Guess_Price')); ?>  </label>
             <input type="number" step="any" name="Guess_Price" value="<?php echo e($item->Guess_Price); ?>" class="form-control">
                                                        </div>
                                           
                                     
                                                 <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput">Cas No  </label>
                                       <input type="text"  name="Cas_No" value="<?php echo e($item->Cas_No); ?>" class="form-control">
                                    </div>
                                                 <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput">HSN  </label>
                                       <input type="text"  name="HSN" value="<?php echo e($item->HSN); ?>" class="form-control">
                                    </div>
                                                 <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput">Uni Code  </label>
                                       <input type="text"  name="Uni_Code" value="<?php echo e($item->Uni_Code); ?>" class="form-control">
                                    </div>
                                                           
                                                           
                                     <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Origin_Number')); ?>  </label>
                                       <input type="text"  name="Origin_Number" value="<?php echo e($item->Origin_Number); ?>" class="form-control">
                                    </div>
                                                              <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Origin_Country')); ?>  </label>
                                       <input type="text"  name="Origin_Country" value="<?php echo e($item->Origin_Country); ?>" class="form-control">
                                    </div>
                                     
                                                              <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.SearchCode1')); ?>  </label>
                                       <input type="text"  name="SearchCode1" value="<?php echo e($item->SearchCode1); ?>" class="form-control">
                                    </div>
                                     
                                                              <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.SearchCode2')); ?>  </label>
                                       <input type="text"  name="SearchCode2" value="<?php echo e($item->SearchCode2); ?>" class="form-control">
                                    </div>                           
                                              
                                                           
                                                               <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Space')); ?>  </label>
                                       <input type="text"  name="Space" value="<?php echo e($item->Space); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Storage')); ?>  </label>
                                       <input type="text"  name="Storage" value="<?php echo e($item->Storage); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Processor')); ?>  </label>
                                       <input type="text"  name="Processor" value="<?php echo e($item->Processor); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Camera')); ?>  </label>
                                       <input type="text"  name="Camera" value="<?php echo e($item->Camera); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Screen')); ?>  </label>
                                       <input type="text"  name="Screen" value="<?php echo e($item->Screen); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.OS')); ?>  </label>
                                       <input type="text"  name="OS" value="<?php echo e($item->OS); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Battery')); ?>  </label>
                                       <input type="text"  name="Battery" value="<?php echo e($item->Battery); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Warranty')); ?>  </label>
                                       <input type="text"  name="Warranty" value="<?php echo e($item->Warranty); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Color')); ?>  </label>
                                       <input type="text"  name="Color" value="<?php echo e($item->Color); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Category')); ?>  </label>
                                       <input type="text"  name="Category" value="<?php echo e($item->Category); ?>" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Model')); ?>  </label>
                                       <input type="text"  name="Model" value="<?php echo e($item->Model); ?>" class="form-control">
                                    </div>
                                                                              <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Arrange')); ?>  </label>
                                       <input type="number"  name="Arrange" value="<?php echo e($item->Arrange); ?>" class="form-control">
                                    </div>                                        
                                                           
                                                           <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Calories')); ?>  </label>
                                       <input type="text"  name="Calories" value="<?php echo e($item->Calories); ?>" class="form-control">
                                    </div>
                                     
           
                                                           
                                                        <div class="form-group col-md-2">
                                                            <label class="form-label" for=""> <?php echo e(trans('admin.Tax')); ?> </label>
                                                            <select class="select2 form-control w-100" name="Tax">
                                                        <option value=""><?php echo e(trans('admin.Tax')); ?></option>
                                                            <?php $__currentLoopData = $Taxes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tax): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
            <option value="<?php echo e($tax->id); ?>" <?php if($item->Tax == $tax->id): ?> selected <?php endif; ?> ><?php echo e($tax->Name); ?></option>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                                                            </select>
                                                        </div>
                                                        <div class="form-group col-md-2">
                                                  <label class="form-label" for=""> <?php echo e(trans('admin.Validity')); ?> </label>
                               <select class="select2 form-control w-100" name="Validity" id="Validity" onchange="ShowD()">
                  <option value="0"  <?php if($item->Validity == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.NO')); ?></option>
                  <option value="1" <?php if($item->Validity == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                                                            </select>
                                                        </div>
                                                           
                                                                    
                                                    <div class="form-group col-md-2">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Code_Typeee')); ?> </label>
                       <select class="select2 form-control w-100" name="Code_Type">
                         <option value=""><?php echo e(trans('admin.Code_Typeee')); ?></option>   
           <option value="GS1"  <?php if($item->Code_Type == 'GS1'): ?> selected <?php endif; ?>>GS1</option>
           <option value="EGS"  <?php if($item->Code_Type == 'EGS'): ?> selected <?php endif; ?>>EGS</option>
                                       </select>
                                    </div>
                                     
                                                   <div class="form-group col-md-2">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.World_Code')); ?> </label>
                                       <input type="text"  name="World_Code" value="<?php echo e($item->World_Code); ?>" class="form-control">
                                    </div>
                                     
                                    <div class="form-group col-md-2" id="Days_Notify" style="display: none">
                                                            <label class="form-label" for="simpleinput">
                                                                <?php echo e(trans('admin.Days_Notify')); ?>

                                                                 </label>
                 <input type="number"  name="Days_Notify" value="<?php echo e($item->Days_Notify); ?>" class="form-control">
                                                        </div>
                                                         <div class="form-group col-md-2">
                                             <label class="form-label" for=""><?php echo e(trans('admin.Store_Show')); ?>  </label>
                             <select class="select2 form-control w-100" name="Store_Show" id="Store_Show" onchange="Show()">
                 <option value="0" <?php if($item->Store_Show == 0): ?> selected <?php endif; ?> ><?php echo e(trans('admin.NO')); ?></option>
                 <option value="1" <?php if($item->Store_Show == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Storee')); ?></option>
                 <option value="2" <?php if($item->Store_Show == 2): ?> selected <?php endif; ?>><?php echo e(trans('admin.Price_List')); ?></option>
                 <option value="3" <?php if($item->Store_Show == 3): ?> selected <?php endif; ?>><?php echo e(trans('admin.Both')); ?></option>
                                                            </select>
                                                        </div>
                           <div class="form-group col-md-2" id="StoreType" style="display: none">
                                              <label class="form-label" for=""> <?php echo e(trans('admin.Store_Type')); ?> </label>
                                                            <select class="select2 form-control w-100" name="Store_Type">
                                                              <option value=""><?php echo e(trans('admin.Store_Type')); ?></option>
                <option value="0" <?php if($item->Store_Type == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Recently')); ?></option>
                <option value="1" <?php if($item->Store_Type == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Offer')); ?></option>
                <option value="2" <?php if($item->Store_Type == 2): ?> selected <?php endif; ?>><?php echo e(trans('admin.Most_Salling')); ?></option>
                                                            </select>
                                                        </div>
                                                        </div>
                                                          <div class="row">
                                                              
                                                                                           <div class="form-group col-md-3" style="display: none">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Ar_Desc')); ?>  </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       <?php echo e(old('Ar_Desc')); ?>

                                       </textarea>  
                                    </div>
                                                              
                                                        <div class="form-group col-md-3">
                                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Ar_Desc')); ?>  </label>
                                                            <textarea class="js-summernote" id="saveToLocal" name="Ar_Desc">
                                                            <?php echo e($item->Ar_Desc); ?>

                                                            </textarea>  
                                                        </div>
                                                        <div class="form-group col-md-3">
                                                            <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.En_Desc')); ?>   </label>
                                                     <textarea class="js-summernote" id="saveToLocal" name="En_Desc">
                                                            <?php echo e($item->En_Desc); ?>

                                                            </textarea>
                                                        </div>
                                                              
                                                                  <div class="form-group col-md-2">
                                       <label class="form-label" for=""><?php echo e(trans('admin.Show_Other_Store')); ?>  </label>
                                       <select class="select2 form-control w-100" name="Show_Other_Store">
                                          <option value="0"  <?php if($item->Show_Other_Store == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.NO')); ?></option>
                                          <option value="1"  <?php if($item->Show_Other_Store == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                             
                                       </select>
                                    </div>                    
                                     
   <div class="form-group col-md-3">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Arabic_Brief_Desc')); ?>  </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Arabic_Brief_Desc">
                                       <?php echo e($item->Arabic_Brief_Desc); ?>

                                       </textarea>  
                                    </div>
                                    <div class="form-group col-md-3">
                                       <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.English_Brief_Desc')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="English_Brief_Desc">
                                       <?php echo e($item->English_Brief_Desc); ?>

                                       </textarea>
                                    </div>
                                     
                                     
                                                              
                                                              
                                                              
                                                        <div class="form-group col-md-3">
                                                            <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Ar_Spec')); ?>   </label>
                                                                 <textarea class="js-summernote" id="saveToLocal" name="Ar_Spec">
                                                            <?php echo e($item->Ar_Spec); ?>

                                                            </textarea>
                                                        </div>
                                                        <div class="form-group col-md-3">
                                                            <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.En_Spec')); ?>   </label>
                                                          <textarea class="js-summernote" id="saveToLocal" name="En_Spec">
                                                            <?php echo e($item->En_Spec); ?>

                                                            </textarea>
                                                        </div>
                                                        </div>
                                                       
                                                           
                                     
                                                         <div class="row">  
                                                        <div class="form-group col-md-3 mb-3">
                                           <label class="form-label" for=""> <?php echo e(trans('admin.Sub_Images')); ?></label>

                                            <input type="file" name="SubImage[]" multiple>                
                                                        </div>
                                                           
                                                           
                                                            <?php $__currentLoopData = $subs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sub): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>        
                                <div class="col-md-10">
                    <img src="../<?php echo e(Storage::url($sub->Image)); ?>" class="control-img">                
                                    </div> 
                            <div class="col-md-2">
                            <a href="<?php echo e(url('DelSubImage/'.$sub->id)); ?>" class="btn btn-default"><i class="fal fa-times"></i></a>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>                         
                                                  </div>         
                                                       </div>
                                                    
                                                    
                                  <div class="tab-pane fade" id="tab_borders_icons-3" role="tabpanel">
                                 <div class="form-row">
                         
                                    <div class="form-group col-md-8">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Product')); ?> </label>
                       <select class="select2 form-control w-100"  id="AdditionProduct" onchange="AdditionPlus()">
                               <option value=""><?php echo e(trans('admin.Product')); ?></option>
   
                           <?php $__currentLoopData = $AdditionsProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $addd): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                           
                                  <option value="<?php echo e($addd->id); ?>"><?php echo e(app()->getLocale() == 'ar' ?$addd->P_Ar_Name :$addd->P_En_Name); ?></option>
                           <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                       </select>
                                    </div>
                                     
                      
                                     
                                            <div class="form-group col-md-4">
                            <button type="button" onclick="InsertAddition()" class="btn btn-default" id="addAdditionP" style="display: none"><i class="fal fa-plus"></i></button>
                                     </div>
                                     
                                     
           <!-- datatable start -->
                                                                <div id="mobile-overflow">
                                                                <table id=""
                                                                    class="table table-bordered table-hover table-striped w-100 mobile-width">
                                                                    <thead>
                                                                        <tr>
                                                                        <th><?php echo e(trans('admin.Product')); ?></th>
                                                                        <th><?php echo e(trans('admin.Actions')); ?></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody id="dataAddition">
                                                                        
                                                        <?php $__currentLoopData = $Additions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $de): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>                
                                                                        
                                                    <tr>
                                                <td>
                                         <?php if(!empty($de->Additional_Product)): ?>
                                            <?php echo e(app()->getLocale() == 'ar' ?$de->Additional_Product()->first()->P_Ar_Name :$de->Additional_Product()->first()->P_En_Name); ?>        
                                                    
                                          <input type="hidden" name="Additional_Product[]" value="<?php echo e($de->Additional_Product); ?>">                  
                                      <?php endif; ?>             
                                                    
          
                                                </td>                        
                                                <td>
                                    <button id="DelDepP" type="button" class="btn btn-default"><i class="fal fa-trash"></i></button>        
                                                </td>                        
                                                </tr>                 
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>                    
                                                                    </tbody>
                                                                  
                                                                </table>
                                                                </div>
                                                                <!-- datatable end -->
                                                           
                                   
                                    
                                 </div>
                              </div>                    
                                                    
                                                    
                                                    </div>
                                                </div>      
                                       <div class="buttons mt-3">

                 <button type="submit" id="SUBMIT" style="display: none" class="btn btn-primary"><i class="fal fa-save"></i> <?php echo e(trans('admin.Save')); ?> </button>
                                                                          </div>
                                       
                                            </div>
                                        </div>
                            </div>
                        </div>
                    </div>
                    </div>
                    </div>
                      </form>   
                </main>


<?php if(app()->getLocale() == 'ar' ): ?> 
<input type="hidden" id="LANG" value="ar">
<?php else: ?>
<input type="hidden" id="LANG" value="en">
<?php endif; ?>

<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>

    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/dropzone/dropzone.css')); ?>">

<style>
    .dt-buttons{
        display: none;
        
    }
    
    .dataTables_filter{
        display: none;
        
    }
</style>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/dropzone/dropzone.js')); ?>"></script>
    

<!--- Plus -->
<script>
 function PLUS(){
     
     
      $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };
   
  var rowctr = $('#samble').rowCount(); 
    var  P_Type =$('#P_Type').val(); 
   
     if(P_Type == 'Assembly' || P_Type == 'Serial'){
         if(rowctr == 0){
             
                         var Unit =$('#unit').val();
     var Rate =$('#rate').val();
     var Code =$('#code').val();
     var Price =$('#price1').val();
     
     if(Unit == ''  || Rate == ''  ||  Code == '' || Price == ''){
         
        document.getElementById("add-data").style.display = "none";           
     }

     
       if(Unit != ''  && Rate != ''  &&  Code != '' && Price != ''){
         
        document.getElementById("add-data").style.display = "block";           
     }  
           
           
             
         }else{
             
     document.getElementById("add-data").style.display = "none";    
             
         }
         
         
         
     }else{
     
     var Unit =$('#unit').val();
     var Rate =$('#rate').val();
     var Code =$('#code').val();
     var Price =$('#price1').val();
     
     if(Unit == ''  || Rate == ''  ||  Code == '' || Price == ''){
         
        document.getElementById("add-data").style.display = "none";           
     }

     
       if(Unit != ''  && Rate != ''  &&  Code != '' && Price != ''){
         
        document.getElementById("add-data").style.display = "block";           
     }
     }
 }
</script>


<!-- Unit Name -->
<script>
   $(document).ready(function() {
   
                  $('#unit').on('change', function(){
                      var countryId = $(this).val();
                      if(countryId) {
                          $.ajax({
                              url: 'UnitNameFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
   
        
                        $('#UnitID').val(key); 
                    $('#UnitName').val(value); 
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }
   
                  });
   
              });
    
   
function  UnitNameCode(x){
    
    var countryId = $('#UnitAssem'+x).val();
    var Pro = $('#Product'+x).val();
                      if(countryId) {
                          $.ajax({
                              url: 'UnitNameCodeFilterr/'+countryId+'/'+Pro,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
                                      
                        $('#CodeAssem'+x).val(data.code); 
                    $('#UnitNameAssem'+x).val(data.name); 
                    $('#Price'+x).val(data.price); 
                                      

                                  });
                                  
                                  
        var Qty = $("#Qty"+x).val();
 var Price = $("#Price"+x).val();
 
    var result = parseFloat(Qty) *  parseFloat(Price) ;  
       $("#Total"+x).val(result);
       
        var Total = $("#Total"+x).val();
        var UnitID = $("#UnitAssem"+x).val();
       
       
       if(Qty == ''  || Price == ''  ||  Total == '' || UnitID == ''){
         
        document.getElementById("AddBtnPur"+x).style.display = "none";           
     }

     
       if(Qty != ''  && Price != ''  &&  Total != '' && UnitID != ''){
         
        document.getElementById("AddBtnPur"+x).style.display = "block";           
     }  
            
         
                                  
                                  
                                  
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }

}   
    
    
</script>  

<!-- Store Show -->
<script>
 function Show(){
   var  Store_Show =$('#Store_Show').val(); 
     
     if(Store_Show == 1){
         
         document.getElementById("StoreType").style.display = "block";   
     }else{
        document.getElementById("StoreType").style.display = "none";    
         
     }
     
 }
    
    
   $(document).ready(function() {
         var  Store_Show =$('#Store_Show').val(); 
     
     if(Store_Show == 1){
         
         document.getElementById("StoreType").style.display = "block";   
     }else{
        document.getElementById("StoreType").style.display = "none";    
         
     } 
       
           });    
    
    
     $(document).ready(function() {
      document.getElementById("SUBMIT").style.display = "block"; 
     });
</script>

<!-- Days Notify Show -->
<script>
 function ShowD(){
   var  Validity =$('#Validity').val(); 
     
     if(Validity == 1){
         
         document.getElementById("Days_Notify").style.display = "block";   
     }else{
        document.getElementById("Days_Notify").style.display = "none";    
         
     }
     
 }
    
   $(document).ready(function() {
   var  Validity =$('#Validity').val(); 
     
     if(Validity == 1){
         
         document.getElementById("Days_Notify").style.display = "block";   
     }else{
        document.getElementById("Days_Notify").style.display = "none";    
         
     }
       
           });       
</script>

<!-- Assembly and vIrables Show -->
<script>
 function Assembly(){
   var  P_Type =$('#P_Type').val(); 
     
   
     if(P_Type == 'Assembly'){
         
         document.getElementById("HideAssembly").style.display = "block";  
          document.getElementById("HideViraOne").style.display = "none";  
          document.getElementById("HideViraTwo").style.display = "none";  
     document.getElementById("EndDate").style.display = "none"; 
     }else if(P_Type == 'Single_Variable'){
         
         document.getElementById("HideAssembly").style.display = "none";  
          document.getElementById("HideViraOne").style.display = "block";  
          document.getElementById("HideViraTwo").style.display = "none";   
              document.getElementById("EndDate").style.display = "none"; 
         
     }else if(P_Type == 'Duble_Variable'){
            document.getElementById("HideAssembly").style.display = "none";  
          document.getElementById("HideViraOne").style.display = "none";  
          document.getElementById("HideViraTwo").style.display = "block";  
              document.getElementById("EndDate").style.display = "none"; 
         
     }else if(P_Type == 'Subscribe'){
            document.getElementById("HideAssembly").style.display = "none";  
          document.getElementById("HideViraOne").style.display = "none";  
          document.getElementById("HideViraTwo").style.display = "none";  
          document.getElementById("EndDate").style.display = "block";  
         
     }else{
         
            document.getElementById("HideAssembly").style.display = "none";  
          document.getElementById("HideViraOne").style.display = "none";  
          document.getElementById("HideViraTwo").style.display = "none";  
           document.getElementById("EndDate").style.display = "none";  
         
         
     }
     

   
     } 
    
 $(document).ready(function() {

  var  P_Type =$('#P_Type').val(); 
     
    
     if(P_Type == 'Assembly'){
         
         document.getElementById("HideAssembly").style.display = "block";  
          document.getElementById("HideViraOne").style.display = "none";  
          document.getElementById("HideViraTwo").style.display = "none";  
     document.getElementById("EndDate").style.display = "none"; 
     }else if(P_Type == 'Single_Variable'){
         
         document.getElementById("HideAssembly").style.display = "none";  
          document.getElementById("HideViraOne").style.display = "block";  
          document.getElementById("HideViraTwo").style.display = "none";   
              document.getElementById("EndDate").style.display = "none"; 
         
     }else if(P_Type == 'Duble_Variable'){
            document.getElementById("HideAssembly").style.display = "none";  
          document.getElementById("HideViraOne").style.display = "none";  
          document.getElementById("HideViraTwo").style.display = "block";  
              document.getElementById("EndDate").style.display = "none"; 
         
     }else if(P_Type == 'Subscribe'){
            document.getElementById("HideAssembly").style.display = "none";  
          document.getElementById("HideViraOne").style.display = "none";  
          document.getElementById("HideViraTwo").style.display = "none";  
          document.getElementById("EndDate").style.display = "block";  
         
     }else{
         
            document.getElementById("HideAssembly").style.display = "none";  
          document.getElementById("HideViraOne").style.display = "none";  
          document.getElementById("HideViraTwo").style.display = "none";  
           document.getElementById("EndDate").style.display = "none";  
         
         
     }
     
           });  
</script>

<!-- Genrte Code  -->
<script>
  function Genrte(){
      
    
   $( "#code" ).val(Math.floor(Math.random() * 1000000));
   
 
      
      
  }
    
    
</script>

<!--  Filter Assembly -->
<script>
   $(document).ready(function(){
   
    fetch_customer_data();
   
    function fetch_customer_data(search = '')
    {
     $.ajax({
      url:'22/AssemblyFilter',
      method:'GET',
      data:{search:search},
      dataType:'json',
      success:function(data)
      {
       $('#data').html(data.table_data);
      }
     })
    }
    
  $(document).on('keyup', '#search', function(){
     var search = $(this).val();     
     fetch_customer_data(search);
    });
  
       
   });
</script>


<!-- Add Assembly -->
<script>
   function Fun(r) { 
       
             var P_Ar_Name = $("#P_Ar_Name"+r).val();
             var P_En_Name= $("#P_En_Name"+r).val();
             var Product = $("#Product"+r).val();
             var UnitID = $("#UnitAssem"+r).val();
             var UnitName = $("#UnitNameAssem"+r).val();
             var Qty = $("#Qty"+r).val();
             var Barcode = $("#CodeAssem"+r).val();
             var Price = $("#Price"+r).val();
             var Total = $("#Total"+r).val();

             document.getElementById("AddBtnPur"+r).style.display = "none";
             document.getElementById("Row"+r).style.display = "none";
          var LANG = $("#LANG").val();
       if(LANG == 'ar' ){ 
          var Nemo = P_Ar_Name ;
          }else{
             var Nemo = P_En_Name ;   
          }
      
             var markup = "<tr><td><input type='hidden' name='P_Ar_NameAssem[]' value='"+P_Ar_Name+"'><input type='hidden' name='P_En_NameAssem[]' value='"+P_En_Name+"'>" + Nemo + "</td><td><input type='hidden' name='P_CodeAssem[]' value='"+Barcode+"'>" + Barcode + "</td><td><input type='hidden' name='UnitAssem[]' value='"+UnitID+"'>" + UnitName + "</td><td><input type='hidden' name='QtyAssem[]' value='"+Qty+"'>" + Qty + "</td><td><input type='hidden' name='PriceAssem[]' value='"+Price+"'>" + Price + "</td><td><input type='hidden' name='TotalAssem[]' value='"+Total+"'>" + Total + "</td><td>  <button id='DelAssem' type='button' class='btn btn-default'><i class='fal fa-trash'></i></button><input id='Product_IDInp"+r+"' type='hidden' name='ProductAssem[]' value='"+Product+"'></td></tr>";
   
            var  Product_IDInp =$("#Product_IDInp"+r).val();
       
         if(Product != Product_IDInp){
             $("#data-dt").append(markup);
         }
       
      
        $('#data-dt').on('click', '#DelAssem', function(e){
                $(this).closest('tr').remove(); 
                    })  
       
       
     }    
</script> 

<!-- Total Assembly -->
<script>
   function AssTotal(r) { 
 var Qty = $("#Qty"+r).val();
 var Price = $("#Price"+r).val();
 
    var result = parseFloat(Qty) *  parseFloat(Price) ;  
       $("#Total"+r).val(result);
       
        var Total = $("#Total"+r).val();
        var UnitID = $("#UnitAssem"+r).val();
       
       
       if(Qty == ''  || Price == ''  ||  Total == '' || UnitID == ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "none";           
     }

     
       if(Qty != ''  && Price != ''  &&  Total != '' && UnitID != ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "block";           
     }  
            
       
       
       
       
   }
</script>


<?php if($item->P_Type == 'Single_Variable'): ?>
<!-- Filter Virable One -->
<script>
   $(document).ready(function(){
   
    fetch_customer_data();
   
    function fetch_customer_data(search = '')
    {
     $.ajax({
      url:'1/VOneFilter',
      method:'GET',
      data:{search:search},
      dataType:'json',
      success:function(data)
      {
       $('.ViraOne').html(data.table_data);
      }
     })
    }
    
  $(document).on('change', '#VOne', function(){
     var search = $(this).val();  
     fetch_customer_data(search);
    });
  
       
   });
    
    
     $(document).ready(function(){
   
    fetch_customer_data();
   
    function fetch_customer_data(search = '')
    {
     $.ajax({
      url:'1/VOneFilter',
      method:'GET',
      data:{search:search},
      dataType:'json',
      success:function(data)
      {
       $('.ViraOne').html(data.table_data);
      }
     })
    }

     var search = $('#VOne').val();  
     fetch_customer_data(search);

       
   });
</script>
<?php endif; ?>

<!-- Filter Virable Two -->
<script>
   $(document).ready(function(){
    
    fetch_customer_data();
   
    function fetch_customer_data(search = '',searchT='')
    {
     $.ajax({
      url:'1/VTwoFilter',
      method:'GET',
      data:{search:search,searchT:searchT},
      dataType:'json',
      success:function(data)
      {
       $('#variables').html(data.table_data);
      }
     })
    }
    
  $(document).on('change', '#VTwo', function(){
     var search = $(this).val();  
     var searchT = $("#VTwoo").val();  
      
            if(search != searchT){  
        document.getElementById("AlertVira").style.display = "none";       
        document.getElementById("TabV2").style.display = "block";   
         fetch_customer_data(search,searchT);     
    }else{
        
        
       document.getElementById("AlertVira").style.display = "block";   
       document.getElementById("TabV2").style.display = "none";   
        
    }
      
   
    });
       
  $(document).on('change', '#VTwoo', function(){
     var searchT = $(this).val();  
     var search = $("#VTwo").val(); 
                  if(search != searchT){  
        document.getElementById("AlertVira").style.display = "none";       
        document.getElementById("TabV2").style.display = "block";   
         fetch_customer_data(search,searchT);     
    }else{
        
        
       document.getElementById("AlertVira").style.display = "block";   
       document.getElementById("TabV2").style.display = "none";   
        
    }     
             
    });
        
  
       
   });
    
    
    
     $(document).ready(function(){
    
    fetch_customer_data();
   
    function fetch_customer_data(search = '',searchT='')
    {
     $.ajax({
      url:'1/VTwoFilter',
      method:'GET',
      data:{search:search,searchT:searchT},
      dataType:'json',
      success:function(data)
      {
       $('#variables').html(data.table_data);
      }
     })
    }
    

     var search = $('#VTwo').val();  
     var searchT = $("#VTwoo").val();  
      
            if(search != searchT){  
        document.getElementById("AlertVira").style.display = "none";       
        document.getElementById("TabV2").style.display = "block";   
         fetch_customer_data(search,searchT);     
    }else{
        
        
       document.getElementById("AlertVira").style.display = "block";   
       document.getElementById("TabV2").style.display = "none";   
        
    }
      
   
 
       
   });
</script>


    <script>
        /* demo scripts for change table color */
        /* change background */


        $(document).ready(function () {

            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });

            var table = $('#dt-basic-example').dataTable(
                {
                    responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: false,

                    dom: "<'row mb-3'<'col-sm-12 col-md-6 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-6 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                    buttons: [
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });

            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
    
     <script>
        var autoSave = $('#autoSave');
        var interval;
        var timer = function()
        {
            interval = setInterval(function()
            {
                //start slide...
                if (autoSave.prop('checked'))
                    saveToLocal();

                clearInterval(interval);
            }, 3000);
        };

        //save
        var saveToLocal = function()
        {
            localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
            console.log("saved");
        }

        //delete 
        var removeFromLocal = function()
        {
            localStorage.removeItem("summernoteData");
            $('#saveToLocal').summernote('reset');
        }

        $(document).ready(function()
        {
            //init default
            $('.js-summernote').summernote(
            {
                height: 200,
                tabsize: 2,
                placeholder: "Type here...",
                dialogsFade: true,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['fontsize', ['fontsize']],
                    ['fontname', ['fontname']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks:
                {
                    //restore from localStorage
                    onInit: function(e)
                    {
                        $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                    },
                    onChange: function(contents, $editable)
                    {
                        clearInterval(interval);
                        timer();
                    }
                }
            });

            //load emojis
            $.ajax(
            {
                url: 'https://api.github.com/emojis',
                async: false
            }).then(function(data)
            {
                window.emojis = Object.keys(data);
                window.emojiUrls = data;
            });

            //init emoji example
            $(".js-hint2emoji").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: 'type starting with : and any alphabet',
                hint:
                {
                    match: /:([\-+\w]+)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(emojis, function(item)
                        {
                            return item.indexOf(keyword) === 0;
                        }));
                    },
                    template: function(item)
                    {
                        var content = emojiUrls[item];
                        return '<img src="' + content + '" width="20" /> :' + item + ':';
                    },
                    content: function(item)
                    {
                        var url = emojiUrls[item];
                        if (url)
                        {
                            return $('<img />').attr('src', url).css('width', 20)[0];
                        }
                        return '';
                    }
                }
            });

            //init mentions example
            $(".js-hint2mention").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: "type starting with @",
                hint:
                {
                    mentions: ['jayden', 'sam', 'alvin', 'david'],
                    match: /\B@(\w*)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(this.mentions, function(item)
                        {
                            return item.indexOf(keyword) == 0;
                        }));
                    },
                    content: function(item)
                    {
                        return '@' + item;
                    }
                }
            });

        });

    </script>
    
     <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }

                $(".js-data-example-ajax").select2(
                {
                    ajax:
                    {
                        url: "https://api.github.com/search/repositories",
                        dataType: 'json',
                        delay: 250,
                        data: function(params)
                        {
                            return {
                                q: params.term, // search term
                                page: params.page
                            };
                        },
                        processResults: function(data, params)
                        {
                            // parse the results into the format expected by Select2
                            // since we are using custom formatting functions we do not need to
                            // alter the remote JSON data, except to indicate that infinite
                            // scrolling can be used
                            params.page = params.page || 1;

                            return {
                                results: data.items,
                                pagination:
                                {
                                    more: (params.page * 30) < data.total_count
                                }
                            };
                        },
                        cache: true
                    },
                    placeholder: 'Search for a repository',
                    escapeMarkup: function(markup)
                    {
                        return markup;
                    }, // let our custom formatter work
                    minimumInputLength: 1,
                    templateResult: formatRepo,
                    templateSelection: formatRepoSelection
                });

                function formatRepo(repo)
                {
                    if (repo.loading)
                    {
                        return repo.text;
                    }

                    var markup = "<div class='select2-result-repository clearfix d-flex'>" +
                        "<div class='select2-result-repository__avatar mr-2'><img src='" + repo.owner.avatar_url + "' class='width-2 height-2 mt-1 rounded' /></div>" +
                        "<div class='select2-result-repository__meta'>" +
                        "<div class='select2-result-repository__title fs-lg fw-500'>" + repo.full_name + "</div>";

                    if (repo.description)
                    {
                        markup += "<div class='select2-result-repository__description fs-xs opacity-80 mb-1'>" + repo.description + "</div>";
                    }

                    markup += "<div class='select2-result-repository__statistics d-flex fs-sm'>" +
                        "<div class='select2-result-repository__forks mr-2'><i class='fal fa-lightbulb'></i> " + repo.forks_count + " Forks</div>" +
                        "<div class='select2-result-repository__stargazers mr-2'><i class='fal fa-star'></i> " + repo.stargazers_count + " Stars</div>" +
                        "<div class='select2-result-repository__watchers mr-2'><i class='fal fa-eye'></i> " + repo.watchers_count + " Watchers</div>" +
                        "</div>" +
                        "</div></div>";

                    return markup;
                }

                function formatRepoSelection(repo)
                {
                    return repo.full_name || repo.text;
                }
            });
        });

    </script>
  
   <script>
   function InsertData(){
   var UnitName = document.getElementById('UnitName').value;
   var UnitID = document.getElementById('UnitID').value;
   var Rate = document.getElementById('rate').value;
   var Code = document.getElementById('code').value;
   var Price1 = document.getElementById('price1').value;
   var Price2 = document.getElementById('price2').value;
   var Price3 = document.getElementById('price3').value;
   var HIDEEE=0;   
   var table =  ` <tr> 
                           <td>
                     ${UnitName}
   <input type="hidden" name="Rate[]" value="${Rate}">
   <input type="hidden" name="Barcode[]" value="${Code}">
   <input type="hidden" name="Price[]" value="${Price1}">
   <input type="hidden" name="Price_Two[]" value="${Price2}">
   <input type="hidden" name="Price_Three[]" value="${Price3}">
   <input type="hidden" id="U${UnitID}" name="Unit[]" value="${UnitID}">
                       </td>
                           <td>${Rate}</td>
                           <td>${Code}</td>
                           <td>${Price1}</td>
                           <td>${Price2}</td>
                           <td>${Price3}</td>
                           <td>
   
           <input type="radio" onclick="DEFAULT(${UnitID})"  name="DefaultUnit"  required>        
           <input type="hidden" class="default" id="def${UnitID}" name="Def[]" value="0">
                          </td>
                           
                           <td>
              <button id="Del" type="button" class="btn btn-default"><i class="fal fa-trash"></i></button>
                           </td>
                        </tr>`;
       
       
       var u = $('#U'+UnitID).val();
                   if(u != UnitID){
                        document.getElementById('data-dt-first').innerHTML += table;
                   }
         $('#unit').val('');
   $('#rate').val('');
   $('#code').val('');
   $('#price1').val('');
   $('#price2').val('');
   $('#price3').val('');
   document.getElementById("add-data").style.display = "none";   

         var rate = [];
$('input[name^="Rate"]').each(function() {
    rate.push(this.value);
});
       
       
   $('input[name^="Rate"]').each(function(key) {

       if(rate[key] == 1){
             HIDEEE += 1 ;
       
          }else{
              HIDEEE += 0 ;
          }
       
        
       });
       
       if(parseFloat(HIDEEE) == 1){
             document.getElementById("SUBMIT").style.display = "block";     
       }else{
                document.getElementById("SUBMIT").style.display = "none";  
       }
             
       
       
       
   $('#data-dt-first').on('click', '#Del', function(e){
   $(this).closest('tr').remove(); 
        var HIDEEE=0;   
        $.fn.rowCount = function() {
   return $('tr', $(this).find('tbody')).length;
   };
   
   var rowctr = $('#samble').rowCount(); 
       
          var rate = [];
$('input[name^="Rate"]').each(function() {
    rate.push(this.value);
});
       
   if(rowctr == 0){
    document.getElementById("SUBMIT").style.display = "none";       
       
   }else{

     document.getElementById("SUBMIT").style.display = "block";   
   }    
       
    $('input[name^="Rate"]').each(function(key) {

       if(rate[key] == 1){
             HIDEEE += 1 ;
       
          }else{
              HIDEEE += 0 ;
          }
       
        
       });
       
       if(parseFloat(HIDEEE) == 1){
             document.getElementById("SUBMIT").style.display = "block";     
       }else{
                document.getElementById("SUBMIT").style.display = "none";  
       }
      
       
   
        
   })        
     
    
 
   }
   
   
   
</script>
<script>
 $('#data-dt-first').on('click', '#Del', function(e){
   $(this).closest('tr').remove(); 
        var HIDEEE=0;   
        $.fn.rowCount = function() {
   return $('tr', $(this).find('tbody')).length;
   };
   
   var rowctr = $('#samble').rowCount(); 
       
          var rate = [];
$('input[name^="Rate"]').each(function() {
    rate.push(this.value);
});
       
   if(rowctr == 0){
    document.getElementById("SUBMIT").style.display = "none";       
       
   }else{

     document.getElementById("SUBMIT").style.display = "block";   
   }    
       
    $('input[name^="Rate"]').each(function(key) {

       if(rate[key] == 1){
             HIDEEE += 1 ;
       
          }else{
              HIDEEE += 0 ;
          }
       
        
       });
       
       if(parseFloat(HIDEEE) == 1){
             document.getElementById("SUBMIT").style.display = "block";     
       }else{
                document.getElementById("SUBMIT").style.display = "none";  
       }
      
       
   
        
   })       
</script>
<script>
 $('#data-dt').on('click', '#DelAssem', function(e){
                $(this).closest('tr').remove(); 
                    })  
</script>


<!-- Addition Product Table -->
<script>

      function InsertAddition() {
       var AdditionProduct=$('#AdditionProduct').val();
       var AdditionProductName=$('#AdditionProduct option:selected').text();
      

      

        var table = ` <tr> 
                                <td>
                            ${AdditionProductName}
          <input type="hidden" name="Additional_Product[]" value="${AdditionProduct}">
                                </td>
         

                                <td>
                    <button id="DelDepP" type="button" class="btn btn-default"><i class="fal fa-trash"></i></button>
                                </td>

                             </tr>`;
        
        document.getElementById("dataAddition").innerHTML += table;
      document.getElementById("addAdditionP").style.display = "none";

        $("#dataAddition").on("click", "#DelDepP", function (e) {
            $(this).closest("tr").remove();

        });
    }
    
    function AdditionPlus(){
        var AdditionProduct=$('#AdditionProduct').val();
        
        if (AdditionProduct != "") {
            document.getElementById("addAdditionP").style.display = "block";
        } 
        
        
         if (AdditionProduct == "") {
             document.getElementById("addAdditionP").style.display = "none";
        } 
        
    }

        $("#dataAddition").on("click", "#DelDepP", function (e) {
            $(this).closest("tr").remove();

        });


</script>


<!-- Default Unit -->
<script>
function DEFAULT(r){
    
    $('.default').val(0);
    
    $('#def'+r).val(1);
    
}
</script>
    <script type="text/javascript">

        $(function () {
        
        $('.dz-details').click(function () {
        
        $('.dz-image').hide();
        
     
        
        });
        
        });
        
        </script>
  
<?php $__env->stopPush(); ?>


<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/admin/Stores/EditItems.blade.php ENDPATH**/ ?>