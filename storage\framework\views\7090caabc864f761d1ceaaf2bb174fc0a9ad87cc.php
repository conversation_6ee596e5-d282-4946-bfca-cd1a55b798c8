<?php
use App\Models\Event;
use App\Models\Products;
use App\Models\CompanyData;
use App\Models\StorsTransfers;
use App\Models\SafeTransfers;
use App\Models\ReciptMaintaince;
use App\Models\Modules;
use App\Models\ModuleSettingsNum;
use App\Models\ReportsSettings;
use App\Models\Notifications;
use App\Models\ManufacturingDefaultData;
$DefManu=ManufacturingDefaultData::orderBy('id','desc')->first();
$Def=CompanyData::orderBy('id','desc')->first();
$NumTransSafes=SafeTransfers::where('Status',0)->count();
$NumTransStroes=StorsTransfers::where('Status',0)->count();
$CountRecipt=ReciptMaintaince::orderBy('id','desc')
->whereIn('Status',[2,7])
->where('Eng',auth()->guard('admin')->user()->emp)
->count();
$Modules=Modules::orderBy('id','desc')->first();
$ModulesSett=ModuleSettingsNum::orderBy('id','desc')->first();
 $ReportsSettings=ReportsSettings::orderBy('id','desc')->first();
if(auth()->guard('admin')->user()->emp == 0){
$NotificationsCount=Notifications::where('Status',0)->count();
$Notifications=Notifications::orderBy('id','desc')->where('Status',0)->take(20)->get();
}else{
$NotificationsCount=Notifications::where('Status',0)->where('Emp',auth()->guard('admin')->user()->emp)->count();
$Notifications=Notifications::orderBy('id','desc')->where('Status',0)->where('Emp',auth()->guard('admin')->user()->emp)->take(20)->get();
}
?>
<?php if(app()->getLocale() == 'ar'): ?>
<!--Arabic style-->
<style>
   .nav-menu li > ul{
   background-color: rgb(71 63 99);
   position: absolute;
   right: 270px;
   top:217px;
   z-index: 1015;
   width:384px;
   padding-inline-start: 0px;
   }
   .nav-menu li{
   position: unset;
   }
   .nav-menu li > ul li a:before{
   content:"";
   width:5px;
   height:5px;
   position:absolute;
   right:10px;
   top:18px;
   border: 1px solid #bfb9c9;
   border-radius: 50%;
   }
   .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul:before{
   display: none;
   }
   .nav-menu li > ul li a{
   padding: 10px 23px;
   }
   .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact)>li>ul>li a:after{
   right:10px;
   left:unset;
   top:18px;
   display:none;
   }
   .nav-function-top .page-sidebar .primary-nav .nav-menu>li>ul{
   right:unset;
   width:unset;
   }
   .nav-function-top .page-sidebar .primary-nav .nav-menu>li>ul:before{
   left:unset;
   right:0;
   }
   #js-nav-menu-wrapper{
   overflow-x:auto!important;
   }
   #js-nav-menu-wrapper-left-btn , #js-nav-menu-wrapper-right-btn{
   display:none!important;
   }
   ::-webkit-scrollbar {
   height: 6px;
   width: 2px;
   }
   *::-webkit-scrollbar-track {
   background: transparent;
   }
   *::-webkit-scrollbar-thumb {
   background-color: #886ab5;
   border-radius: 50px;
   border: 1px solid #8361b6;
   }
   @media (max-width:600px){
   .nav-menu li > ul{
   background-color: rgb(71 63 99);
   position: unset;
   right: unset;
   top:unset;
   z-index: 1015;
   width: unset;
   padding-inline-start: 0px;
   }
   }
</style>
<?php else: ?>
<!--English style-->
<style>
   .nav-menu li > ul{
   background-color: rgb(71 63 99);
   position: absolute;
   left: 270px;
   top:217px;
   z-index: 1015;
   width:384px;
   padding-inline-start: 0px;
   }
   .nav-menu li{
   position: unset;
   }
   .nav-menu li > ul li a:before{
   content:"";
   width:5px;
   height:5px;
   position:absolute;
   left:10px;
   top:18px;
   border: 1px solid #bfb9c9;
   border-radius: 50%;
   }
   .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul:before{
   display: none;
   }
   .nav-menu li > ul li a{
   padding: 10px 23px;
   }
   .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact)>li>ul>li a:after{
   right:10px;
   left:unset;
   top:18px;
   display:none;
   }
   .nav-function-top .page-sidebar .primary-nav .nav-menu>li>ul{
   right:unset;
   width:unset;
   }
   .nav-function-top .page-sidebar .primary-nav .nav-menu>li>ul:before{
   left:unset;
   right:0;
   }
   #js-nav-menu-wrapper{
   overflow-x:auto!important;
   }
   #js-nav-menu-wrapper-left-btn , #js-nav-menu-wrapper-right-btn{
   display:none!important;
   }
   ::-webkit-scrollbar {
   height: 6px;
   width: 2px;
   }
   *::-webkit-scrollbar-track {
   background: transparent;
   }
   *::-webkit-scrollbar-thumb {
   background-color: #886ab5;
   border-radius: 50px;
   border: 1px solid #8361b6;
   }
   @media (max-width:600px){
   .nav-menu li > ul{
   background-color: rgb(71 63 99);
   position: unset;
   right: unset;
   top:unset;
   z-index: 1015;
   width: unset;
   padding-inline-start: 0px;
   }
   }
</style>
<?php endif; ?>
<body class="mod-bg-1 mod-nav-link ">
   <script>
      /**
       *	This script should be placed right after the body tag for fast execution
       *	Note: the script is written in pure javascript and does not depend on thirdparty library
       **/
      'use strict';

      var classHolder = document.getElementsByTagName("BODY")[0],
          /**
           * Load from localstorage
           **/
          themeSettings = (localStorage.getItem('themeSettings')) ? JSON.parse(localStorage.getItem('themeSettings')) :
          {},
          themeURL = themeSettings.themeURL || '',
          themeOptions = themeSettings.themeOptions || '';
      /**
       * Load theme options
       **/
      if (themeSettings.themeOptions)
      {
          classHolder.className = themeSettings.themeOptions;
          console.log("%c✔ Theme settings loaded", "color: #148f32");
      }
      else
      {
          console.log("%c✔ Heads up! Theme settings is empty or does not exist, loading default settings...", "color: #ed1c24");
      }
      if (themeSettings.themeURL && !document.getElementById('mytheme'))
      {
          var cssfile = document.createElement('link');
          cssfile.id = 'mytheme';
          cssfile.rel = 'stylesheet';
          cssfile.href = themeURL;
          document.getElementsByTagName('head')[0].appendChild(cssfile);

      }
      else if (themeSettings.themeURL && document.getElementById('mytheme'))
      {
          document.getElementById('mytheme').href = themeSettings.themeURL;
      }
      /**
       * Save to localstorage
       **/
      var saveSettings = function()
      {
          themeSettings.themeOptions = String(classHolder.className).split(/[^\w-]+/).filter(function(item)
          {
              return /^(nav|header|footer|mod|display)-/i.test(item);
          }).join(' ');
          if (document.getElementById('mytheme'))
          {
              themeSettings.themeURL = document.getElementById('mytheme').getAttribute("href");
          };
          localStorage.setItem('themeSettings', JSON.stringify(themeSettings));
      }
      /**
       * Reset settings
       **/
      var resetSettings = function()
      {
          localStorage.setItem("themeSettings", "");
      }

   </script>
   <div class="page-wrapper">
   <div class="page-inner">
   <!-- BEGIN Left Aside -->
   <aside class="page-sidebar">
      <div class="page-logo">
         <a href="<?php echo e(url('OstAdmin')); ?>" class="page-logo-link press-scale-down d-flex align-items-center position-relative" data-toggle="modal" data-target="#modal-shortcut">
            <!--Logo-->
            <?php if(!empty($Def->Logo)): ?>
            <img src="<?php echo e(URL::to($Def->Logo)); ?>" alt="SmartAdmin WebApp" aria-roledescription="logo">
            <?php else: ?>
            <img src="<?php echo e(asset('Admin/img/logo.png')); ?>" alt="SmartAdmin WebApp" aria-roledescription="logo">
            <?php endif; ?>
            <!-- <span class="page-logo-text mr-1"> -->
            <?php if(!empty($Def->Name)): ?>
            <!-- <?php echo e(app()->getLocale() == 'ar' ?$Def->Name :$Def->NameEn); ?> -->
            <?php else: ?>
            <?php echo e(trans('admin.Ost')); ?>

            <?php endif; ?>
            </span>
            <span class="position-absolute text-white opacity-50 small pos-top pos-right mr-2 mt-n2"></span>
            <i class="fal fa-angle-down d-inline-block ml-1 fs-lg color-primary-300"></i>
         </a>
      </div>
      <!-- BEGIN PRIMARY NAVIGATION -->
      <nav id="js-primary-nav" class="primary-nav" role="navigation">
         <div class="nav-filter">
            <div class="position-relative">
               <input type="text" id="nav_filter_input" placeholder="Filter menu" class="form-control" tabindex="0">
               <a href="#" onclick="return false;" class="btn-primary btn-search-close js-waves-off" data-action="toggle" data-class="list-filter-active" data-target=".page-sidebar">
               <i class="fal fa-chevron-up"></i>
               </a>
            </div>
         </div>
         <div class="info-card">
            <?php if(!empty(auth()->guard('admin')->user()->image)): ?>
            <img src="<?php echo e(auth()->guard('admin')->user()->image); ?>" class="profile-image rounded-circle" alt="Dr. Codex Lantern">
            <?php else: ?>
            <img src="<?php echo e(asset('Admin/img/default.jpeg')); ?>" class="profile-image rounded-circle" alt="Dr. Codex Lantern">
            <?php endif; ?>
            <div class="info-card-text">
               <a href="<?php echo e(url('Profile')); ?>" class="d-flex align-items-center text-white">
               <span class="text-truncate text-truncate-sm d-inline-block">
                    <?php echo e(app()->getLocale() == 'ar' ?auth()->guard('admin')->user()->name :auth()->guard('admin')->user()->nameEn); ?>

               </span>
               </a>
            </div>
            <img src="<?php echo e(asset('Admin/img/card-backgrounds/cover-2-lg.png')); ?>" class="cover" alt="cover">
            <a href="#" onclick="return false;" class="pull-trigger-btn" data-action="toggle" data-class="list-filter-active" data-target=".page-sidebar" data-focus="nav_filter_input">
            <i class="fal fa-angle-down"></i>
            </a>
         </div>
         <ul id="js-nav-menu" class="nav-menu">
            <!--
               <?php if($Modules->Capital  ==  1): ?>
                       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('راس المال')): ?>
                    <li>
                       <a href="#" title="Miscellaneous" data-filter-tags="miscellaneous">
                       <i class="fal fa-globe"></i>
                       <span class="nav-link-text" data-i18n="nav.miscellaneous"> <?php echo e(trans('admin.Capital')); ?>   </span>
                       </a>
                       <ul>

                              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه راس المال')): ?>
                            <li>
                             <a href="<?php echo e(url('Capital')); ?>" title="FullCalendar" data-filter-tags="miscellaneous fullcalendar">
                             <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.Capital')); ?> </span>
                             </a>
                          </li>
               <?php endif; ?>

                              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('صرف ارباح')): ?>
                                          <li>
                             <a href="<?php echo e(url('Spend_Profits')); ?>" title="FullCalendar" data-filter-tags="miscellaneous fullcalendar">
                             <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.Spend_Profits')); ?> </span>
                             </a>
                          </li>
                           <?php endif; ?>

                       </ul>
                    </li>
                     <?php endif; ?>
               <?php endif; ?>
                    -->

        <li>

               <a href="#" title="<?php echo e(trans('admin.Accounts_Reports')); ?>" data-filter-tags="<?php echo e(trans('admin.Accounts_Reports')); ?>">
               <i class="fal fa-cog"></i>
               <span class="nav-link-text" data-i18n="nav.theme_settings">OST erp</span>
                   <input type="hidden" id="inpu24" value="1">
               </a>
               <ul style="width:450px;"  id="menu24">
                  <div class="row">

                     <div class="col-sm-6">


                        <li>
                           <a href="<?php echo e(url('IntroView')); ?>" title="<?php echo e(trans('admin.Intro')); ?>" data-filter-tags="<?php echo e(trans('admin.Intro')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                         <?php echo e(trans('admin.Intro')); ?>

                           </span>
                           </a>
                        </li>


                                       <li>
                           <a href="<?php echo e(url('TermsView')); ?>" title="<?php echo e(trans('admin.Terms')); ?>" data-filter-tags="<?php echo e(trans('admin.Terms')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                         <?php echo e(trans('admin.Terms')); ?>

                           </span>
                           </a>
                        </li>


                                       <li>
                           <a href="<?php echo e(url('ReportIssue')); ?>" title="<?php echo e(trans('admin.ReportIssue')); ?>" data-filter-tags="<?php echo e(trans('admin.ReportIssue')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                         <?php echo e(trans('admin.ReportIssue')); ?>

                           </span>
                           </a>
                        </li>




                                       <li>
                           <a href="<?php echo e(url('RabihEducation')); ?>" title="<?php echo e(trans('admin.Education')); ?>" data-filter-tags="<?php echo e(trans('admin.Education')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                         <?php echo e(trans('admin.Education')); ?>

                           </span>
                           </a>
                        </li>

                     </div>
                  </div>
               </ul>
            </li>

        <?php if(auth()->guard('admin')->user()->vend != 0): ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقارير الحسابات')): ?>
            <li>

               <a href="#" title="<?php echo e(trans('admin.Accounts_Reports')); ?>" data-filter-tags="<?php echo e(trans('admin.Accounts_Reports')); ?>">
               <i class="fal fa-cog"></i>
               <span class="nav-link-text" data-i18n="nav.theme_settings"><?php echo e(trans('admin.Accounts_Reports')); ?></span>
                   <input type="hidden" id="inpu21" value="1">
               </a>
               <ul style="width:450px;"  id="menu21">
                  <div class="row">

                     <div class="col-sm-6">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('كشف حساب موردين')): ?>
                        <li>
                           <a href="<?php echo e(url('Vendor_Account_Statement')); ?>" title="<?php echo e(trans('admin.Vendor_Account_Statement')); ?>" data-filter-tags="<?php echo e(trans('admin.Vendor_Account_Statement')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                           <?php echo e(trans('admin.Vendor_Account_Statement')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                     </div>
                  </div>
               </ul>
            </li>
            <?php endif; ?>

               <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('التقارير')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.Reports')); ?>" data-filter-tags="<?php echo e(trans('admin.Reports')); ?>">
               <i class="fal fa-edit"></i>
               <span class="nav-link-text" data-i18n="nav.form_stuff"><?php echo e(trans('admin.Reports')); ?></span>
                   <input type="hidden" id="inpu20" value="1">
               </a>
               <ul style="width: 1200px;"  id="menu20">
                  <div class="row">

                     <div class="col-sm-3">

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات المجموعات')): ?>
                        <li>
                           <a href="<?php echo e(url('GroupsSales')); ?>" title="<?php echo e(trans('admin.GroupsSales')); ?>" data-filter-tags="<?php echo e(trans('admin.GroupsSales')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.GroupsSales')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>

                             <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات البرندات')): ?>
                               <li>
                           <a href="<?php echo e(url('BrandsSales')); ?>" title="<?php echo e(trans('admin.BrandsSales')); ?>" data-filter-tags="<?php echo e(trans('admin.BrandsSales')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.BrandsSales')); ?></span>
                           </a>
                        </li>
                               <?php endif; ?>
                     </div>



                  </div>
               </ul>
            </li>
            <?php endif; ?>

        <?php elseif(auth()->guard('admin')->user()->cli != 0): ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقارير الحسابات')): ?>
            <li>

               <a href="#" title="<?php echo e(trans('admin.Accounts_Reports')); ?>" data-filter-tags="<?php echo e(trans('admin.Accounts_Reports')); ?>">
               <i class="fal fa-cog"></i>
               <span class="nav-link-text" data-i18n="nav.theme_settings"><?php echo e(trans('admin.Accounts_Reports')); ?></span>
                   <input type="hidden" id="inpu19" value="1">
               </a>
               <ul style="width:450px;"  id="menu19">
                  <div class="row">

                     <div class="col-sm-6">

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('كشف حساب عملاء')): ?>
                        <li>
                           <a href="<?php echo e(url('Customer_Account_Statement')); ?>" title="<?php echo e(trans('admin.Customer_Account_Statement')); ?>" data-filter-tags="<?php echo e(trans('admin.Customer_Account_Statement')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                           <?php echo e(trans('admin.Customer_Account_Statement')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                     </div>
                  </div>
               </ul>
            </li>
            <?php endif; ?>
        <?php else: ?>
            <?php if($Modules->Accounts  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الحسابات')): ?>
            <li>

               <a href="#" title="<?php echo e(trans('admin.Accounts')); ?>" data-filter-tags="<?php echo e(trans('admin.Accounts')); ?>">
               <i class="fal fa-info-circle"></i>
               <span class="nav-link-text" data-i18n="nav.application_intel"><?php echo e(trans('admin.Accounts')); ?></span>
                <input type="hidden" id="inpu" value="1">
               </a>
               <ul style="width:450px" id="menu">
                  <div class="row">
                     <div class="col-sm-6">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الدليل المحاسبي')): ?>
                        <li>
                           <a href="<?php echo e(url('AccountingManual')); ?>" title="<?php echo e(trans('admin.Accounting_Manual')); ?>" data-filter-tags="<?php echo e(trans('admin.Accounting_Manual')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_introduction">
                           <?php echo e(trans('admin.Accounting_Manual')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تحويلات الخزائن')): ?>
                        <li>
                           <a href="<?php echo e(url('SafesTransfer')); ?>" title="<?php echo e(trans('admin.Safes_Transfer')); ?>" data-filter-tags="<?php echo e(trans('admin.Safes_Transfer')); ?>">
                           <span class="nav-link-text" data-i18n="nav.form_stuff_basic_inputs">  <?php echo e(trans('admin.Safes_Transfer')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مصاريف اصول')): ?>
                        <!--      <li>
                           <a href="<?php echo e(url('AssetExpenses')); ?>" title="<?php echo e(trans('admin.AssetExpenses')); ?>" data-filter-tags="<?php echo e(trans('admin.AssetExpenses')); ?>">
                                           <span class="nav-link-text" data-i18n="nav.application_intel_introduction">

                                             <?php echo e(trans('admin.AssetExpenses')); ?>

                                               </span>
                                           </a>
                                        </li>  -->
                        <?php endif; ?>


                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول تحويلات الخزائن')): ?>
                        <li>
                           <a href="<?php echo e(url('SafesTransferSechdule')); ?>" title="<?php echo e(trans('admin.Safes_Transfer_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Safes_Transfer_Sechdule')); ?>">
                           <span style="color: #e980d5;padding-left: 3px;"><?php echo e($NumTransSafes); ?></span>
                           <span class="nav-link-text" data-i18n="nav.form_stuff_basic_inputs">  <?php echo e(trans('admin.Safes_Transfer_Sechdule')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه قيد يومي')): ?>
                        <li>
                           <a href="<?php echo e(url('Journalizing')); ?>" title="<?php echo e(trans('admin.Journalizing')); ?>" data-filter-tags="<?php echo e(trans('admin.Journalizing')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_privacy"> <?php echo e(trans('admin.Journalizing')); ?>    </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه سند قبض')): ?>
                        <li>
                           <a href="<?php echo e(url('Receipt_Voucher')); ?>" title="<?php echo e(trans('admin.Receipt_Voucher')); ?>" data-filter-tags="<?php echo e(trans('admin.Receipt_Voucher')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_privacy">
                           <?php echo e(trans('admin.Receipt_Voucher')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه سند صرف')): ?>
                        <li>
                           <a href="<?php echo e(url('Payment_Voucher')); ?>" title="<?php echo e(trans('admin.Payment_Voucher')); ?>" data-filter-tags="<?php echo e(trans('admin.Payment_Voucher')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_privacy">
                           <?php echo e(trans('admin.Payment_Voucher')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه قيد افتتاحي')): ?>
                      <!--  <li>
                           <a href="<?php echo e(url('OpeningEntries')); ?>" title="<?php echo e(trans('admin.Opening_Entries')); ?>" data-filter-tags="<?php echo e(trans('admin.Opening_Entries')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_privacy">
                           <?php echo e(trans('admin.Opening_Entries')); ?>

                           </span>
                           </a>
                        </li>  -->
                        <?php endif; ?>
                                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الشيكات الصادره')): ?>
                        <li>
                           <a href="<?php echo e(url('Exporting_Checks')); ?>" title="<?php echo e(trans('admin.Exporting_Checks')); ?>" data-filter-tags="<?php echo e(trans('admin.Exporting_Checks')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_privacy">
                           <?php echo e(trans('admin.Exporting_Checks')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الشيكات الوارده')): ?>
                        <li>
                           <a href="<?php echo e(url('Incoming_checks')); ?>" title="<?php echo e(trans('admin.Incoming_checks')); ?>" data-filter-tags="<?php echo e(trans('admin.Incoming_checks')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_privacy">
                           <?php echo e(trans('admin.Incoming_checks')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('وصل امانه')): ?>
                        <li>
                           <a href="<?php echo e(url('Insurance_Paper')); ?>" title="<?php echo e(trans('admin.Insurance_Paper')); ?>" data-filter-tags="<?php echo e(trans('admin.Insurance_Paper')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_privacy">
                           <?php echo e(trans('admin.Insurance_Paper')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الضرائب')): ?>
                        <li>
                           <a href="<?php echo e(url('Taxes')); ?>" title="<?php echo e(trans('admin.Taxes')); ?>" data-filter-tags="<?php echo e(trans('admin.Taxes')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_community_support">
                           <?php echo e(trans('admin.Taxes')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                     </div>
                     <div class="col-sm-6">
                             <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الاصول')): ?>
                        <li>
                           <a href="<?php echo e(url('Assets')); ?>" title="<?php echo e(trans('admin.Assets')); ?>" data-filter-tags="<?php echo e(trans('admin.Assets')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_introduction">
                           <?php echo e(trans('admin.Assets')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                                                 <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الخزائن و البنوك')): ?>
                        <li>
                           <a href="<?php echo e(url('Safes_Banks')); ?>" title="<?php echo e(trans('admin.Safes_Banks')); ?>" data-filter-tags="<?php echo e(trans('admin.Safes_Banks')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_privacy">
                           <?php echo e(trans('admin.Safes_Banks')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                                 <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الفروع')): ?>
                        <li>
                           <a href="<?php echo e(url('Branches')); ?>" title="<?php echo e(trans('admin.Branches')); ?>" data-filter-tags="<?php echo e(trans('admin.Branches')); ?>">
                           <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.Branches')); ?> </span>
                           </a>
                        </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول القيود اليوميه')): ?>
                        <li>
                           <a href="<?php echo e(url('JournalizingSechdule')); ?>" title="<?php echo e(trans('admin.Journalizing_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Journalizing_Sechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.form_stuff_basic_inputs">  <?php echo e(trans('admin.Journalizing_Sechdule')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول القيود الافتتاحيه')): ?>
                    <!--    <li>
                           <a href="<?php echo e(url('Opening_EntriesSechdule')); ?>" title="<?php echo e(trans('admin.Opening_Entries_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Opening_Entries_Sechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.form_stuff_basic_inputs">  <?php echo e(trans('admin.Opening_Entries_Sechdule')); ?></span>
                           </a>
                        </li> -->
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول سند القبض')): ?>
                        <li>
                           <a href="<?php echo e(url('Receipt_VoucherSechdule')); ?>" title="<?php echo e(trans('admin.Receipt_Voucher_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Receipt_Voucher_Sechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.form_stuff_basic_inputs">  <?php echo e(trans('admin.Receipt_Voucher_Sechdule')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول سند الصرف')): ?>
                        <li>
                           <a href="<?php echo e(url('Payment_VoucherSechdule')); ?>" title="<?php echo e(trans('admin.Payment_Voucher_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Payment_Voucher_Sechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.form_stuff_basic_inputs">  <?php echo e(trans('admin.Payment_Voucher_Sechdule')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                                   <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('العملات')): ?>
                        <li>
                           <a href="<?php echo e(url('Coins')); ?>" title="<?php echo e(trans('admin.Coins')); ?>" data-filter-tags="<?php echo e(trans('admin.Coins')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_marketing_dashboard">
                           <?php echo e(trans('admin.Coins')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مراكز التكلفه')): ?>
                        <li>
                           <a href="<?php echo e(url('CostCenters')); ?>" title="<?php echo e(trans('admin.Cost_Centers')); ?>" data-filter-tags="<?php echo e(trans('admin.Cost_Centers')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_marketing_dashboard">
                           <?php echo e(trans('admin.Cost_Centers')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع الشيكات')): ?>
                        <li>
                           <a href="<?php echo e(url('Checks_Type')); ?>" title="<?php echo e(trans('admin.Checks_Type')); ?>" data-filter-tags="<?php echo e(trans('admin.Checks_Type')); ?>">
                           <span class="nav-link-text" data-i18n="nav.application_intel_marketing_dashboard">
                           <?php echo e(trans('admin.Checks_Type')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الشركاء')): ?>
                     <!---   <li>
                           <a href="<?php echo e(url('Partners')); ?>" title="<?php echo e(trans('admin.Partners')); ?>" data-filter-tags="<?php echo e(trans('admin.Partners')); ?>">
                           <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.Partners')); ?> </span>
                           </a>
                        </li> -->
                        <?php endif; ?>

                     </div>
                  </div>
               </ul>
            </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقارير الحسابات')): ?>
            <li>

               <a href="#" title="<?php echo e(trans('admin.Accounts_Reports')); ?>" data-filter-tags="<?php echo e(trans('admin.Accounts_Reports')); ?>">
               <i class="fal fa-cog"></i>
               <span class="nav-link-text" data-i18n="nav.theme_settings"><?php echo e(trans('admin.Accounts_Reports')); ?></span>
                   <input type="hidden" id="inpu1" value="1">
               </a>
               <ul style="width:600px;"  id="menu1">
                  <div class="row">
                     <div class="col-sm-6">

                               <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('كشف حساب عملاء')): ?>
                        <li>
                           <a href="<?php echo e(url('Customer_Account_Statement')); ?>" title="<?php echo e(trans('admin.Customer_Account_Statement')); ?>" data-filter-tags="<?php echo e(trans('admin.Customer_Account_Statement')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                           <?php echo e(trans('admin.Customer_Account_Statement')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('كشف حساب موردين')): ?>
                        <li>
                           <a href="<?php echo e(url('Vendor_Account_Statement')); ?>" title="<?php echo e(trans('admin.Vendor_Account_Statement')); ?>" data-filter-tags="<?php echo e(trans('admin.Vendor_Account_Statement')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                           <?php echo e(trans('admin.Vendor_Account_Statement')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                               <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('كشف حساب خزنه، بنك')): ?>
                        <li>
                           <a href="<?php echo e(url('Safe_Bank_Statement')); ?>" title="<?php echo e(trans('admin.Safe_Bank_Statement')); ?>" data-filter-tags="<?php echo e(trans('admin.Safe_Bank_Statement')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database"><?php echo e(trans('admin.Safe_Bank_Statement')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                                       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ارصده الحسابات')): ?>
                        <li>
                           <a href="<?php echo e(url('Account_Balances')); ?>" title="<?php echo e(trans('admin.Account_Balances')); ?>" data-filter-tags="<?php echo e(trans('admin.Account_Balances')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_skin_options"> <?php echo e(trans('admin.Account_Balances')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ارصده العملاء')): ?>
                        <li>
                           <a href="<?php echo e(url('Customer_Balances')); ?>" title="<?php echo e(trans('admin.Customer_Balances')); ?>" data-filter-tags="<?php echo e(trans('admin.Customer_Balances')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                           <?php echo e(trans('admin.Customer_Balances')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('دفتر الاستاذ')): ?>
                        <li>
                           <a href="<?php echo e(url('Ledger')); ?>" title="<?php echo e(trans('admin.Ledger')); ?>" data-filter-tags="<?php echo e(trans('admin.Ledger')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_theme_modes_(beta)"><?php echo e(trans('admin.Ledger')); ?> </span>
                           </a>
                        </li>
                        <?php endif; ?>
                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('قائمه الدخل')): ?>
                        <li>
                           <a href="<?php echo e(url('Incom_List')); ?>" title="<?php echo e(trans('admin.Incom_List')); ?>" data-filter-tags="<?php echo e(trans('admin.Incom_List')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                           <?php echo e(trans('admin.Incom_List')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('قائمه المركز المالي')): ?>
                        <li>
                           <a href="<?php echo e(url('Financial_Center')); ?>" title="<?php echo e(trans('admin.Financial_Center')); ?>" data-filter-tags="<?php echo e(trans('admin.Financial_Center')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                           <?php echo e(trans('admin.Financial_Center')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                              <?php if($ReportsSettings->SubIncomList == 1): ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير قائمه الدخل الفرعي')): ?>
                  <li>
                           <a href="<?php echo e(url('SubIncomList')); ?>" title="<?php echo e(trans('admin.SubIncomList')); ?>" data-filter-tags="<?php echo e(trans('admin.SubIncomList')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.SubIncomList')); ?></span>
                           </a>
                        </li>
                          <?php endif; ?>
                        <?php endif; ?>


                            <?php if($ReportsSettings->ClientAccountStatement == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير كشف حساب عميل مفصل')): ?>

                                                      <li>
                           <a href="<?php echo e(url('ClientAccountStatement')); ?>" title="<?php echo e(trans('admin.ClientAccountStatement')); ?>" data-filter-tags="<?php echo e(trans('admin.ClientAccountStatement')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ClientAccountStatement')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>
                         <?php if($ReportsSettings->ClientsStatement == 1): ?>
                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير كشف حساب عملاء')): ?>

                                                            <li>
                           <a href="<?php echo e(url('ClientsStatement')); ?>" title="<?php echo e(trans('admin.ClientsStatement')); ?>" data-filter-tags="<?php echo e(trans('admin.ClientsStatement')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ClientsStatement')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>
                         <?php if($ReportsSettings->VendorAccountStatement == 1): ?>
                              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير كشف حساب مورد مفصل')): ?>
                                                   <li>
                           <a href="<?php echo e(url('VendorAccountStatement')); ?>" title="<?php echo e(trans('admin.VendorAccountStatement')); ?>" data-filter-tags="<?php echo e(trans('admin.VendorAccountStatement')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.VendorAccountStatement')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>
                         <?php if($ReportsSettings->VendorsStatement == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير كشف حساب موردين')): ?>
                                                            <li>
                           <a href="<?php echo e(url('VendorsStatement')); ?>" title="<?php echo e(trans('admin.VendorsStatement')); ?>" data-filter-tags="<?php echo e(trans('admin.VendorsStatement')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.VendorsStatement')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>

                            <?php if($ReportsSettings->Customer_Debts == 1): ?>

                                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اعمار ديون العملاء')): ?>
                               <li>
                           <a href="<?php echo e(url('Customer_Debts')); ?>" title="<?php echo e(trans('admin.Customer_Debts')); ?>" data-filter-tags="<?php echo e(trans('admin.Customer_Debts')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Customer_Debts')); ?></span>
                           </a>
                        </li>
                               <?php endif; ?>
                         <?php endif; ?>




                     </div>
                     <div class="col-sm-6">

                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اليوميه العامه')): ?>
                        <li>
                           <a href="<?php echo e(url('General_Daily')); ?>" title="<?php echo e(trans('admin.General_Daily')); ?>" data-filter-tags="<?php echo e(trans('admin.General_Daily')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_how_it_works">
                           <?php echo e(trans('admin.General_Daily')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ميزان المراجعه')): ?>
                        <li>
                           <a href="<?php echo e(url('Trial_Balance')); ?>" title="<?php echo e(trans('admin.Trial_Balance')); ?>" data-filter-tags="<?php echo e(trans('admin.Trial_Balance')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_layout_options">
                           <?php echo e(trans('admin.Trial_Balance')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>


                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقارير الشيكات')): ?>
                        <li>
                           <a href="<?php echo e(url('Checks_Reports')); ?>" title="<?php echo e(trans('admin.Checks_Reports')); ?>" data-filter-tags="<?php echo e(trans('admin.Checks_Reports')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                           <?php echo e(trans('admin.Checks_Reports')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                                                 <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير وصلات الامانه')): ?>
                        <li>
                           <a href="<?php echo e(url('InsurancePaperReport')); ?>" title="<?php echo e(trans('admin.InsurancePaperReport')); ?>" data-filter-tags="<?php echo e(trans('admin.InsurancePaperReport')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                           <?php echo e(trans('admin.InsurancePaperReport')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقارير مركز التكلفه')): ?>
                        <li>
                           <a href="<?php echo e(url('Cost_Centers_Report')); ?>" title="<?php echo e(trans('admin.Cost_Centers_Report')); ?>" data-filter-tags="<?php echo e(trans('admin.Cost_Centers_Report')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                           <?php echo e(trans('admin.Cost_Centers_Report')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير الاصول الثابته')): ?>
                        <li>
                           <a href="<?php echo e(url('Fixed_Assets_Report')); ?>" title="<?php echo e(trans('admin.Fixed_Assets_Report')); ?>" data-filter-tags="<?php echo e(trans('admin.Fixed_Assets_Report')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                           <?php echo e(trans('admin.Fixed_Assets_Report')); ?>

                           </span>
                           </a>

                        </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ارصده الخزائن و البنوك')): ?>
                        <li>
                           <a href="<?php echo e(url('Safes_Balances')); ?>" title="<?php echo e(trans('admin.Safes_Balances')); ?>" data-filter-tags="<?php echo e(trans('admin.Safes_Balances')); ?>">
                           <span class="nav-link-text" data-i18n="nav.theme_settings_saving_to_database">
                           <?php echo e(trans('admin.Safes_Balances')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>


                           <?php if($ReportsSettings->ExpensesList == 1): ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير قائمه المصاريف')): ?>
                                                                                 <li>
                           <a href="<?php echo e(url('ExpensesList')); ?>" title="<?php echo e(trans('admin.ExpensesList')); ?>" data-filter-tags="<?php echo e(trans('admin.ExpensesList')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ExpensesList')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>

                         <?php endif; ?>


                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اجمالي مصاريف الخزائن')): ?>
                            <?php if($ReportsSettings->TotalExpensesSafes == 1): ?>
                            <li>
                           <a href="<?php echo e(url('TotalExpensesSafes')); ?>" title=" <?php echo e(trans('admin.TotalExpensesSafes')); ?>" data-filter-tags="<?php echo e(trans('admin.TotalExpensesSafes')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.TotalExpensesSafes')); ?>  </span>
                           </a>
                        </li>
                            <?php endif; ?>
                         <?php endif; ?>


                           <?php if($ReportsSettings->ExpensesReport == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير المصاريف')): ?>
                        <li>
                           <a href="<?php echo e(url('ExpensesReport')); ?>" title="<?php echo e(trans('admin.ExpensesReport')); ?>" data-filter-tags="<?php echo e(trans('admin.ExpensesReport')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ExpensesReport')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>



                           <?php if($ReportsSettings->SafesTransferReport == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير تحويلات خزائن')): ?>
                                                    <li>
                           <a href="<?php echo e(url('SafesTransferReport')); ?>" title="<?php echo e(trans('admin.SafesTransferReport')); ?>" data-filter-tags="<?php echo e(trans('admin.SafesTransferReport')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.SafesTransferReport')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>


    <?php if($ReportsSettings->IncomListReport == 1): ?>
                                                   <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير قائمه المقبوضات')): ?>
                                                                                 <li>
                           <a href="<?php echo e(url('IncomListReport')); ?>" title="<?php echo e(trans('admin.IncomListReport')); ?>" data-filter-tags="<?php echo e(trans('admin.IncomListReport')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.IncomListReport')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                                <?php if($ReportsSettings->InstallmentReport == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الاقساط الغير مدفوعه')): ?>
                        <li style="width: 100%">
                           <a href="<?php echo e(url('InstallmentReport')); ?>" title="<?php echo e(trans('admin.InstallmentReport')); ?>" data-filter-tags="<?php echo e(trans('admin.InstallmentReport')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.InstallmentReport')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                              <?php if($ReportsSettings->Vendor_Debts == 1): ?>

                                                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اعمار ديون الموردين')): ?>
                               <li>
                           <a href="<?php echo e(url('Vendor_Debts')); ?>" title="<?php echo e(trans('admin.Vendor_Debts')); ?>" data-filter-tags="<?php echo e(trans('admin.Vendor_Debts')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Vendor_Debts')); ?></span>
                           </a>
                        </li>
                               <?php endif; ?>
                         <?php endif; ?>




                     </div>
                  </div>
               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>
            <?php if($Modules->Stores  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المخازن')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.Supply_Chain')); ?>" data-filter-tags="<?php echo e(trans('admin.Supply_Chain')); ?>">
               <i class="fal fa-book"></i>
               <span class="nav-link-text" data-i18n="nav.documentation"><?php echo e(trans('admin.Supply_Chain')); ?> </span>
                <input type="hidden" id="inpu2" value="1">
               </a>
               <ul style="width:450px;"  id="menu2">
                  <div class="row">
                     <div class="col-sm-6">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('عرض المخازن')): ?>
                        <li>
                           <a href="<?php echo e(url('Stores')); ?>" title="<?php echo e(trans('admin.Stores')); ?>" data-filter-tags="<?php echo e(trans('admin.Stores')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_general_docs"> <?php echo e(trans('admin.Stores')); ?> </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('دليل الاصناف')): ?>
                        <li>
                           <a href="<?php echo e(url('ItemsGuide')); ?>" title="<?php echo e(trans('admin.Items_Guide')); ?>" data-filter-tags="<?php echo e(trans('admin.Items_Guide')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_premium_support">  <?php echo e(trans('admin.Items_Guide')); ?>   </span>
                           </a>
                        </li>
                        <li>
                           <a href="<?php echo e(url('ItemsGuide2')); ?>" title="<?php echo e(trans('admin.Items_Guide')); ?>" data-filter-tags="<?php echo e(trans('admin.Items_Guide')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_premium_support"> <?php echo e(trans('admin.New_Items_Guide')); ?>   </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه صنف')): ?>
                        <li>
                           <a href="<?php echo e(url('Add_Items')); ?>" title="<?php echo e(trans('admin.Add_Items')); ?>" data-filter-tags="<?php echo e(trans('admin.Add_Items')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.Add_Items')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول الاصناف')): ?>
                        <li>
                           <a href="<?php echo e(url('Products_Sechdule')); ?>" title="<?php echo e(trans('admin.Products_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Products_Sechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.Products_Sechdule')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه تحويلات المخازن')): ?>
                        <li>
                           <a href="<?php echo e(url('StoresTransfers')); ?>" title="<?php echo e(trans('admin.Stores_Transfers')); ?>" data-filter-tags="<?php echo e(trans('admin.Stores_Transfers')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_build_notes">
                           <?php echo e(trans('admin.Stores_Transfers')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول تحويلات المخازن')): ?>
                        <li>
                           <a href="<?php echo e(url('StoresTransfersSechdule')); ?>" title="<?php echo e(trans('admin.Stores_Transfers_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Stores_Transfers_Sechdule')); ?>">
                           <span style="color: #e980d5;padding-left: 3px;"><?php echo e($NumTransStroes); ?></span>
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.Stores_Transfers_Sechdule')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اذن استلام بضاعه')): ?>
                                 <li>
                           <a href="<?php echo e(url('Permission_to_receive_goods')); ?>" title="<?php echo e(trans('admin.Permission_to_receive_goods')); ?>" data-filter-tags="<?php echo e(trans('admin.Permission_to_receive_goods')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_flavors_&_editions">
                               <?php echo e(trans('admin.Permission_to_receive_goods')); ?>

                                </span>
                           </a>
                           </li>
                           <?php endif; ?>

                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول استلام بضاعه')): ?>
                           <li>
                           <a href="<?php echo e(url('ReceiveGoodsSechdule')); ?>" title="<?php echo e(trans('admin.ReceiveGoodsSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ReceiveGoodsSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_flavors_&_editions">
                               <?php echo e(trans('admin.ReceiveGoodsSechdule')); ?>

                                </span>
                           </a>
                           </li>
                           <?php endif; ?>
                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اذن صرف بضاعه')): ?>
                             <li>
                           <a href="<?php echo e(url('Permission_to_exchange_goods')); ?>" title="<?php echo e(trans('admin.Permission_to_exchange_goods')); ?>" data-filter-tags="<?php echo e(trans('admin.Permission_to_exchange_goods')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_flavors_&_editions">
                               <?php echo e(trans('admin.Permission_to_exchange_goods')); ?>

                                </span>
                           </a>
                           </li>
                           <?php endif; ?>

                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول صرف بضاعه')): ?>
                           <li>
                           <a href="<?php echo e(url('ExchangeGoodsSechdule')); ?>" title="<?php echo e(trans('admin.ExchangeGoodsSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ExchangeGoodsSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_flavors_&_editions">
                               <?php echo e(trans('admin.ExchangeGoodsSechdule')); ?>

                                </span>
                           </a>
                           </li>
                           <?php endif; ?>

                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تالف')): ?>
                        <li>
                           <a href="<?php echo e(url('Consists')); ?>" title="<?php echo e(trans('admin.Consists')); ?>" data-filter-tags="<?php echo e(trans('admin.Consists')); ?>">
                           <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.Consists')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول التوالف')): ?>
                        <li>
                           <a href="<?php echo e(url('ConsistsSechdule')); ?>" title="<?php echo e(trans('admin.ConsistsSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ConsistsSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.ConsistsSechdule')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>


                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه اصناف بدايه فتره')): ?>
                        <li>
                           <a href="<?php echo e(url('StartPeriodProducts')); ?>" title="<?php echo e(trans('admin.Start_Period_Products')); ?>" data-filter-tags="<?php echo e(trans('admin.Start_Period_Products')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.Start_Period_Products')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول اصناف بدايه فتره')): ?>
                        <li>
                           <a href="<?php echo e(url('StartPeriodSechdule')); ?>" title="<?php echo e(trans('admin.Start_Period_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Start_Period_Sechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.Start_Period_Sechdule')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>


                                 <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تغيير الاسعار')): ?>
                              <li>
                           <a href="<?php echo e(url('NewChangePrice')); ?>" title="<?php echo e(trans('admin.ChangePrices')); ?>" data-filter-tags="<?php echo e(trans('admin.ChangePrices')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_premium_support"> <?php echo e(trans('admin.ChangePrices')); ?>   </span>
                           </a>
                        </li>
                         <?php endif; ?>

                     </div>
                     <div class="col-sm-6">
                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('وحدات القياس')): ?>
                        <li>
                           <a href="<?php echo e(url('Measurement_Units')); ?>" title="<?php echo e(trans('admin.Measurement_Units')); ?>" data-filter-tags="<?php echo e(trans('admin.Measurement_Units')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_project_structure">
                           <?php echo e(trans('admin.Measurement_Units')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مجموعه الاصناف')): ?>
                        <li>
                           <a href="<?php echo e(url('Items_Groups')); ?>" title="<?php echo e(trans('admin.Items_Groups')); ?>" data-filter-tags="<?php echo e(trans('admin.Items_Groups')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_flavors_&_editions">
                           <?php echo e(trans('admin.Items_Groups')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الصناعه و الماركات')): ?>
                        <li>
                           <a href="<?php echo e(url('Manufacture')); ?>" title="<?php echo e(trans('admin.Manufacture')); ?>" data-filter-tags="<?php echo e(trans('admin.Manufacture')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_community_support">
                           <?php echo e(trans('admin.Manufacture')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المتغيرات')): ?>
                        <li>
                           <a href="<?php echo e(url('Virables')); ?>" title="<?php echo e(trans('admin.Virables')); ?>" data-filter-tags="<?php echo e(trans('admin.Virables')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_community_support">
                           <?php echo e(trans('admin.Virables')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه جرد')): ?>
                        <li>
                           <a href="<?php echo e(url('Inventory')); ?>" title="<?php echo e(trans('admin.Inventory')); ?>" data-filter-tags="<?php echo e(trans('admin.Inventory')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.Inventory')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول الجرد')): ?>
                        <li>
                           <a href="<?php echo e(url('Inventory_Sechdule')); ?>" title="<?php echo e(trans('admin.Inventory_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Inventory_Sechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.Inventory_Sechdule')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول التسويه')): ?>
                        <li>
                           <a href="<?php echo e(url('Settlement_Sechdule')); ?>" title="<?php echo e(trans('admin.Settlement_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Settlement_Sechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.Settlement_Sechdule')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>


                       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اعدادات طباعه الباركود')): ?>
                        <li>
                           <a href="<?php echo e(url('BarcodeـPrinting_Settings')); ?>" title="<?php echo e(trans('admin.BarcodeـPrinting_Settings')); ?>" data-filter-tags="<?php echo e(trans('admin.BarcodeـPrinting_Settings')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.BarcodeـPrinting_Settings')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>

                       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('طباعه الباركود')): ?>
                        <li>
                           <a href="<?php echo e(url('BarcodeـPrinting')); ?>" title="<?php echo e(trans('admin.BarcodeـPrinting')); ?>" data-filter-tags="<?php echo e(trans('admin.BarcodeـPrinting')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.BarcodeـPrinting')); ?>

                           </span>
                           </a>
                        </li>
                            <li>
                           <a href="<?php echo e(url('QRـPrinting')); ?>" title="<?php echo e(trans('admin.QRـPrinting')); ?>" data-filter-tags="<?php echo e(trans('admin.QRـPrinting')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.QRـPrinting')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('شركات الشحن')): ?>
                        <li>
                           <a href="<?php echo e(url('ShippingCompany')); ?>" title="<?php echo e(trans('admin.Shipping_Compaines')); ?>" data-filter-tags="<?php echo e(trans('admin.Shipping_Compaines')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_general_docs"> <?php echo e(trans('admin.Shipping_Compaines')); ?> </span>
                           </a>
                        </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع الاشتراكات')): ?>
                        <li>
                           <a href="<?php echo e(url('SubscribeTypes')); ?>" title="<?php echo e(trans('admin.SubscribeTypes')); ?>" data-filter-tags="<?php echo e(trans('admin.SubscribeTypes')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_community_support">
                           <?php echo e(trans('admin.SubscribeTypes')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>


                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول مبيعات  تحويلات المخازن')): ?>
                        <li>
                           <a href="<?php echo e(url('Stores_Sales_Transfers_Sechdule')); ?>" title="<?php echo e(trans('admin.Stores_Sales_Transfers_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Stores_Sales_Transfers_Sechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.Stores_Sales_Transfers_Sechdule')); ?>

                           </span>
                           </a>
                        </li>
                         <li>
                           <a href="<?php echo e(url('ReturnStoresTransfersSechdule')); ?>" title="<?php echo e(trans('admin.ReturnStoresTransfersSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ReturnStoresTransfersSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.documentation_licensing">
                           <?php echo e(trans('admin.ReturnStoresTransfersSechdule')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>


                        <?php if(in_array(auth()->guard('admin')->user()->email, ['<EMAIL>', '<EMAIL>', '<EMAIL>'])): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('كميات المخازن')): ?>
                        <li>
                           <a href="<?php echo e(url('StoresQty')); ?>" title="<?php echo e(trans('admin.StoresQty')); ?>" data-filter-tags="<?php echo e(trans('admin.StoresQty')); ?>">
                           <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.StoresQty')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php endif; ?>




                     </div>
                  </div>
               </ul>
            </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المشتريات')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.Purchases_P')); ?>" data-filter-tags="<?php echo e(trans('admin.Purchases_P')); ?>">
               <i class="fal fa-window"></i>
               <span class="nav-link-text" data-i18n="nav.ui_components"><?php echo e(trans('admin.Purchases_P')); ?></span>
                   <input type="hidden" id="inpu3" value="1">
               </a>
               <ul style="width:450px"  id="menu3">
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الموردين')): ?>
                  <li>
                     <a href="<?php echo e(url('Vendors')); ?>" title="<?php echo e(trans('admin.Vendors')); ?>" data-filter-tags="<?php echo e(trans('admin.Vendors')); ?>">
                     <span class="nav-link-text" data-i18n="nav.tables_generate_table_style"> <?php echo e(trans('admin.Vendors')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه امر شراء')): ?>
                  <li>
                     <a href="<?php echo e(url('PurchasesOrder')); ?>" title="<?php echo e(trans('admin.PurchasesOrder')); ?>" data-filter-tags="<?php echo e(trans('admin.PurchasesOrder')); ?>">
                     <span class="nav-link-text" data-i18n="nav.ui_components_alerts"><?php echo e(trans('admin.PurchasesOrder')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول آوامر الشراء')): ?>
                  <li>
                     <a href="<?php echo e(url('PurchasesOrderSechdule')); ?>" title="<?php echo e(trans('admin.PurchasesOrderSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.PurchasesOrderSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.ui_components_breadcrumbs"><?php echo e(trans('admin.PurchasesOrderSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه مشتريات')): ?>
                  <li>
                     <a href="<?php echo e(url('Purchases')); ?>" title="<?php echo e(trans('admin.Purchases')); ?>" data-filter-tags="<?php echo e(trans('admin.Purchases')); ?>">
                     <span class="nav-link-text" data-i18n="nav.ui_components_accordions"><?php echo e(trans('admin.Purchases')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول المشتريات')): ?>
                  <li>
                     <a href="<?php echo e(url('PurchasesSechdule')); ?>" title="<?php echo e(trans('admin.PurchasesSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.PurchasesSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.PurchasesSechdule')); ?></span>
                     </a>
                  </li>
                  <li>
                     <a href="<?php echo e(url('PurchasesSechduleTax')); ?>" title="<?php echo e(trans('admin.PurchasesSechduleTax')); ?>" data-filter-tags="<?php echo e(trans('admin.PurchasesSechduleTax')); ?>">
                     <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.PurchasesSechduleTax')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول مرتجع المشتريات')): ?>
                  <li>
                     <a href="<?php echo e(url('ReturnPurchasesSechdule')); ?>" title="<?php echo e(trans('admin.ReturnPurchasesSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ReturnPurchasesSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.ui_components_badges">
                     <?php echo e(trans('admin.ReturnPurchasesSechdule')); ?>

                     </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الفواتير المعلقه مشتريات')): ?>
                  <li>
                     <a href="<?php echo e(url('PurchasesHold')); ?>" title="<?php echo e(trans('admin.PurchasesHold')); ?>" data-filter-tags="<?php echo e(trans('admin.PurchasesHold')); ?>">
                     <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.PurchasesHold')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المنتجات المستلمه مشتريات')): ?>
                  <li>
                     <a href="<?php echo e(url('Recived_Products')); ?>" title="<?php echo e(trans('admin.Recived_Products')); ?>" data-filter-tags="<?php echo e(trans('admin.Recived_Products')); ?>">
                     <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.Recived_Products')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('النواقص')): ?>
                  <li>
                     <a href="<?php echo e(url('Shortcomings')); ?>" title="<?php echo e(trans('admin.Shortcomings')); ?>" data-filter-tags="<?php echo e(trans('admin.Shortcomings')); ?>">
                     <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.Shortcomings')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول النواقص')): ?>
                  <li>
                     <a href="<?php echo e(url('ShortcomingsSechdule')); ?>" title="<?php echo e(trans('admin.ShortcomingsSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ShortcomingsSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.ShortcomingsSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
               </ul>
            </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المبيعات')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.Sales')); ?>" data-filter-tags="<?php echo e(trans('admin.Sales')); ?>">
               <i class="fal fa-bolt"></i>
               <span class="nav-link-text" data-i18n="nav.utilities"><?php echo e(trans('admin.Sales')); ?> </span>
                   <input type="hidden" id="inpu4" value="1">
               </a>
               <ul style="width:450px"  id="menu4">
                  <div class="row">
                     <div class="col-sm-6">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('نقاط البيع')): ?>
                        <?php if(auth()->guard('admin')->user()->emp != 0): ?>
                        <li>
                           <a href="<?php echo e(url('POS')); ?>" title="<?php echo e(trans('admin.POS')); ?>" data-filter-tags="<?php echo e(trans('admin.POS')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_clearfix"> <?php echo e(trans('admin.POS')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه  مبيعات')): ?>
                        <li>
                           <a href="<?php echo e(url('Sales')); ?>" title="<?php echo e(trans('admin.Sales')); ?>" data-filter-tags="<?php echo e(trans('admin.Sales')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_clearfix"> <?php echo e(trans('admin.Sales')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول المبيعات')): ?>
                        <li>
                           <a href="<?php echo e(url('SalesSechdule')); ?>" title="<?php echo e(trans('admin.SalesSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_fonts"> <?php echo e(trans('admin.SalesSechdule')); ?></span>
                           </a>
                        </li>
                        <li>
                           <a href="<?php echo e(url('SalesSechduleTax')); ?>" title="<?php echo e(trans('admin.SalesSechduleTax')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesSechduleTax')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_fonts"> <?php echo e(trans('admin.SalesSechduleTax')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه امر بيع')): ?>
                        <li>
                           <a href="<?php echo e(url('SalesOrder')); ?>" title="<?php echo e(trans('admin.SalesOrder')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesOrder')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders">  <?php echo e(trans('admin.SalesOrder')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول امر البيع')): ?>
                        <li>
                           <a href="<?php echo e(url('SalesOrderSechdule')); ?>" title="<?php echo e(trans('admin.SalesOrderSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesOrderSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_flexbox"><?php echo e(trans('admin.SalesOrderSechdule')); ?> </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه عرض سعر')): ?>
                        <li>
                           <a href="<?php echo e(url('Quote')); ?>" title="<?php echo e(trans('admin.Quote')); ?>" data-filter-tags="<?php echo e(trans('admin.Quote')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Quote')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                      <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('عرض سعر مصور')): ?>
                        <li>
                           <a href="<?php echo e(url('QuoteImages')); ?>" title="<?php echo e(trans('admin.QuoteImages')); ?>" data-filter-tags="<?php echo e(trans('admin.QuoteImages')); ?>">
                           <span class="nav-link-text" data-i18n="nav.form_stuff_checkbox_&_radio"><?php echo e(trans('admin.QuoteImages')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول عرض سعر')): ?>
                        <li>
                           <a href="<?php echo e(url('Quote_Sechdule')); ?>" title="<?php echo e(trans('admin.Quote_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Quote_Sechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_display_property">  <?php echo e(trans('admin.Quote_Sechdule')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول عرض سعر مصور')): ?>
                        <li>
                           <a href="<?php echo e(url('QuoteImagesSechdule')); ?>" title="<?php echo e(trans('admin.QuoteImagesSechdules')); ?>" data-filter-tags="<?php echo e(trans('admin.QuoteImagesSechdules')); ?>">
                           <span class="nav-link-text" data-i18n="nav.form_stuff_checkbox_&_radio"><?php echo e(trans('admin.QuoteImagesSechdules')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول المبيعات')): ?>
                        <li>
                           <a href="<?php echo e(url('SalesDeliverySechdule')); ?>" title="<?php echo e(trans('admin.SalesDeliverySechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesDeliverySechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_fonts"> <?php echo e(trans('admin.SalesDeliverySechdule')); ?></span>
                           </a>
                        </li>
                        <?php if(auth()->guard('admin')->user()->emp != 0): ?>
                        <li>
                           <a href="<?php echo e(url('MyRequestDelivery')); ?>" title="<?php echo e(trans('admin.MyRequestDelivery')); ?>" data-filter-tags="<?php echo e(trans('admin.MyRequestDelivery')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_fonts"> <?php echo e(trans('admin.MyRequestDelivery')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php endif; ?>
                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول مرتجع المبيعات')): ?>
                        <li>
                           <a href="<?php echo e(url('ReturnSalesSechdule')); ?>" title="<?php echo e(trans('admin.Return_Sales_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Return_Sales_Sechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_flexbox"> <?php echo e(trans('admin.Return_Sales_Sechdule')); ?></span>
                           </a>
                        </li>
                             <li>
                           <a href="<?php echo e(url('ReturnSalesSechduleTaxPage')); ?>" title="{trans('admin.Return_Sales_TaxSechdule')}}" data-filter-tags="{trans('admin.Return_Sales_TaxSechdule')}}">
                           <span class="nav-link-text" data-i18n="nav.utilities_flexbox"> <?php echo e(trans('admin.Return_Sales_TaxSechdule')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>



                     </div>
                     <div class="col-sm-6">
                             <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('العملاء')): ?>
                        <li>
                           <a href="<?php echo e(url('Clients')); ?>" title="<?php echo e(trans('admin.Clients')); ?>" data-filter-tags="<?php echo e(trans('admin.Clients')); ?>">
                           <span class="nav-link-text" data-i18n="nav.form_stuff_checkbox_&_radio"><?php echo e(trans('admin.Clients')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مجموعه العملاء')): ?>
                        <li>
                           <a href="<?php echo e(url('Clients_Group')); ?>" title="<?php echo e(trans('admin.Clients_Group')); ?>" data-filter-tags="<?php echo e(trans('admin.Clients_Group')); ?>">
                           <span class="nav-link-text" data-i18n="nav.form_stuff_checkbox_&_radio"><?php echo e(trans('admin.Clients_Group')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه عملاء')): ?>
                        <li>
                           <a href="<?php echo e(url('AddClients')); ?>" title="<?php echo e(trans('admin.Add_Clients')); ?>" data-filter-tags="<?php echo e(trans('admin.Add_Clients')); ?>">
                           <span class="nav-link-text" data-i18n="nav.form_stuff_checkbox_&_radio"><?php echo e(trans('admin.Add_Clients')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>


                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات اشتراكات')): ?>
                        <li>
                           <a href="<?php echo e(url('SalesSubscribes')); ?>" title="<?php echo e(trans('admin.SalesSubscribes')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesSubscribes')); ?>">
                           <span class="nav-link-text" data-i18n="nav.form_stuff_checkbox_&_radio"><?php echo e(trans('admin.SalesSubscribes')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول مبيعات اشتراكات')): ?>
                        <li>
                           <a href="<?php echo e(url('SalesSubscribesSechdule')); ?>" title="<?php echo e(trans('admin.SalesSubscribesSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesSubscribesSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.form_stuff_checkbox_&_radio"><?php echo e(trans('admin.SalesSubscribesSechdule')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الفواتير المعلقه مبيعات')): ?>
                        <li>
                           <a href="<?php echo e(url('SalesHoldSechdule')); ?>" title="<?php echo e(trans('admin.SalesHoldSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesHoldSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_fonts"> <?php echo e(trans('admin.SalesHoldSechdule')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المنتجات المستلمه مبيعات')): ?>
                        <li>
                           <a href="<?php echo e(url('RecivedProductsSales')); ?>" title="<?php echo e(trans('admin.Recived_Products')); ?>" data-filter-tags="<?php echo e(trans('admin.Recived_Products')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_fonts"> <?php echo e(trans('admin.Recived_Products')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول الاقساط مبيعات')): ?>
                        <li>
                           <a href="<?php echo e(url('InstallmentSechdule')); ?>" title="<?php echo e(trans('admin.InstallmentSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.InstallmentSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_clearfix"> <?php echo e(trans('admin.InstallmentSechdule')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>


                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('شركات التقسيط')): ?>
                                    <li>
                           <a href="<?php echo e(url('InstallmentCompanies')); ?>" title="<?php echo e(trans('admin.InstallmentCompanies')); ?>" data-filter-tags="<?php echo e(trans('admin.InstallmentCompanies')); ?>">
                           <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.InstallmentCompanies')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('قائمه الاسعار')): ?>
                                    <li>
                           <a href="<?php echo e(url('StorePriceList')); ?>" title="<?php echo e(trans('admin.StorePriceListt')); ?>" data-filter-tags="<?php echo e(trans('admin.StorePriceListt')); ?>">
                           <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.StorePriceListt')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>

                              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مرتجع بدون فاتوره')): ?>
                                        <li>
                           <a href="<?php echo e(url('ReturnWithoutBill')); ?>" title="<?php echo e(trans('admin.ReturnWithoutBill')); ?>" data-filter-tags="<?php echo e(trans('admin.ReturnWithoutBill')); ?>">
                           <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.ReturnWithoutBill')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>


                                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اوامر الشغل')): ?>
                                        <li>
                           <a href="<?php echo e(url('JobOrder')); ?>" title="<?php echo e(trans('admin.JobOrder')); ?>" data-filter-tags="<?php echo e(trans('admin.JobOrder')); ?>">
                           <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.JobOrder')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>


                                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول اوامر الشغل')): ?>
                                        <li>
                           <a href="<?php echo e(url('JobOrderSechdule')); ?>" title="<?php echo e(trans('admin.JobOrderSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.JobOrderSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.JobOrderSechdule')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>

                                                   <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول تنفيذ اوامر الشغل')): ?>
                                        <li>
                           <a href="<?php echo e(url('ExecuteJobOrderSechdule')); ?>" title="<?php echo e(trans('admin.ExecuteJobOrderSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ExecuteJobOrderSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.ExecuteJobOrderSechdule')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>



                     </div>
                  </div>
               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>
              <?php if($Modules->Stores  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('التقارير')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.SupplyChainReports')); ?>" data-filter-tags="<?php echo e(trans('admin.SupplyChainReports')); ?>">
               <i class="fal fa-edit"></i>
               <span class="nav-link-text" data-i18n="nav.form_stuff"><?php echo e(trans('admin.SupplyChainReports')); ?></span>
                   <input type="hidden" id="inpu5" value="1">
               </a>
               <ul style="width: 1200px;"  id="menu5">
                  <div class="row">

                     <div class="col-sm-3">


                           <?php if($ReportsSettings->Product_Info == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الاستعلام عن منتج')): ?>
                        <li>
                           <a href="<?php echo e(url('Product_Info')); ?>" title="<?php echo e(trans('admin.Product_Info')); ?>" data-filter-tags="<?php echo e(trans('admin.Product_Info')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Product_Info')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                           <?php if($ReportsSettings->StoresInventory == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جرد المخازن')): ?>
                        <li>
                           <a href="<?php echo e(url('StoresInventory')); ?>" title="<?php echo e(trans('admin.StoresInventory')); ?>" data-filter-tags="<?php echo e(trans('admin.StoresInventory')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StoresInventory')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جرد المخازن')): ?>
  <?php if($ReportsSettings->StoresInventoryy == 1): ?>
                            <li>
                           <a href="<?php echo e(url('StoresInventoryNew')); ?>" title="<?php echo e(trans('admin.StoresInventory')); ?>" data-filter-tags="<?php echo e(trans('admin.StoresInventory')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StoresInventory')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
         <?php endif; ?>




                    <?php if($ReportsSettings->StagnantItemss == 1): ?>
                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اصناف راكده')): ?>
                        <li>
                           <a href="<?php echo e(url('StagnantItemsTwo')); ?>" title="<?php echo e(trans('admin.StagnantItems')); ?>" data-filter-tags="<?php echo e(trans('admin.StagnantItems')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StagnantItems')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                           <?php if($ReportsSettings->StagnantItems == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اصناف راكده')): ?>
                        <li>
                           <a href="<?php echo e(url('StagnantItems')); ?>" title="<?php echo e(trans('admin.StagnantItems')); ?>" data-filter-tags="<?php echo e(trans('admin.StagnantItems')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StagnantItems')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>


                           <?php if($ReportsSettings->ItemsMoves == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حركه الاصناف')): ?>
                        <li>
                           <a href="<?php echo e(url('ItemsMoves')); ?>" title="<?php echo e(trans('admin.ItemsMoves')); ?>" data-filter-tags="<?php echo e(trans('admin.ItemsMoves')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ItemsMoves')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>


                           <?php if($ReportsSettings->ProductMoveDetails == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير حزكه صنف تفصيلي')): ?>
                                                               <li>
                           <a href="<?php echo e(url('ProductMoveDetails')); ?>" title="<?php echo e(trans('admin.ProductMoveDetails')); ?>" data-filter-tags="<?php echo e(trans('admin.ProductMoveDetails')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ProductMoveDetails')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>


                                         <?php if($ReportsSettings->ProductProfits == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير ارباح اصناف')): ?>
                        <li>
                           <a href="<?php echo e(url('ProductProfitsNew')); ?>" title=" <?php echo e(trans('admin.ProductProfits')); ?>" data-filter-tags="<?php echo e(trans('admin.ProductProfits')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ProductProfits')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>
                     <?php if($ReportsSettings->ExceptProductProfits == 1): ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير ارباح اصناف متوقعه')): ?>
                        <li>
                           <a href="<?php echo e(url('ProductProfits')); ?>" title=" <?php echo e(trans('admin.ExceptProductProfits')); ?>" data-filter-tags="<?php echo e(trans('admin.ExceptProductProfits')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ExceptProductProfits')); ?>  </span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>

                           <?php if($ReportsSettings->ExceptProfits == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الارباح المتوقعه')): ?>
                        <li>
                           <a href="<?php echo e(url('ExceptProfits')); ?>" title=" <?php echo e(trans('admin.ExceptProfits')); ?>" data-filter-tags="<?php echo e(trans('admin.ExceptProfits')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ExceptProfits')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                                    <?php if($ReportsSettings->ProfitSalesProduct == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير ربحيه اصناف مباعه')): ?>
                                    <li>
                           <a href="<?php echo e(url('ProfitSalesProduct')); ?>" title="<?php echo e(trans('admin.ProfitSalesProduct')); ?>" data-filter-tags="<?php echo e(trans('admin.ProfitSalesProduct')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ProfitSalesProduct')); ?></span>
                           </a>
                        </li>
                  <?php endif; ?>
                            <?php endif; ?>



                    <?php if($ReportsSettings->MostSalesProducts == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير الاصناف الاكثر مبيعا')): ?>
                                                                   <li>
                           <a href="<?php echo e(url('MostSalesProducts')); ?>" title="<?php echo e(trans('admin.MostSalesProducts')); ?>" data-filter-tags="<?php echo e(trans('admin.MostSalesProducts')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.MostSalesProducts')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>


                           <?php if($ReportsSettings->Products == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير المنتجات')): ?>
                        <li>
                           <a href="<?php echo e(url('ProductsReports')); ?>" title="<?php echo e(trans('admin.Products')); ?>" data-filter-tags="<?php echo e(trans('admin.Products')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Products')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>
      <?php if($ReportsSettings->DailyProducts == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الاصناف اليوميه')): ?>
                        <li>
                           <a href="<?php echo e(url('DailyProducts')); ?>" title="<?php echo e(trans('admin.DailyProducts')); ?>" data-filter-tags="<?php echo e(trans('admin.DailyProducts')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.DailyProducts')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>



                     </div>

                     <div class="col-sm-3">

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اجمالي الحركات اليوميه')): ?>
                       <!-- <li>
                           <a href="<?php echo e(url('TotalDailyMoves')); ?>" title="<?php echo e(trans('admin.TotalDailyMoves')); ?>" data-filter-tags="utilities borders">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.TotalDailyMoves')); ?></span>
                           </a>
                        </li>  -->
                        <?php endif; ?>

                           <?php if($ReportsSettings->CreditStores == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تكلفه المخزون')): ?>
                        <li>
                           <a href="<?php echo e(url('CreditStores')); ?>" title="<?php echo e(trans('admin.CreditStores')); ?>" data-filter-tags="<?php echo e(trans('admin.CreditStores')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.CreditStores')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                                           <?php if($ReportsSettings->StoresBalances == 1): ?>
                      <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ارصده المخزون')): ?>
                                                                                 <li>
                           <a href="<?php echo e(url('StoresBalancesNew')); ?>" title="<?php echo e(trans('admin.StoresBalances')); ?>" data-filter-tags="<?php echo e(trans('admin.StoresBalances')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StoresBalances')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                    <?php if($ReportsSettings->StoresBalancesCat == 1): ?>
                      <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ارصده المخزون')): ?>
                                                                                 <li>
                           <a href="<?php echo e(url('StoresBalancesCatNew')); ?>" title="<?php echo e(trans('admin.StoresBalancesCat')); ?>" data-filter-tags="{trans('admin.StoresBalancesCat')}}">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StoresBalancesCat')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                  <?php endif; ?>




                                <?php if($ReportsSettings->StoresBalancesTwo == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ارصده المخزون')): ?>
                        <li>
                           <a href="<?php echo e(url('StoresBalances')); ?>" title="<?php echo e(trans('admin.StoresBalancesTwo')); ?>" data-filter-tags="<?php echo e(trans('admin.StoresBalancesTwo')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StoresBalancesTwo')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>


                                       <?php if($ReportsSettings->StoresCost == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تكلفه المخزون')): ?>
                        <li>
                           <a href="<?php echo e(url('StoresCost')); ?>" title="<?php echo e(trans('admin.StoresCost')); ?>" data-filter-tags="<?php echo e(trans('admin.StoresCost')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StoresCost')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>



                           <?php if($ReportsSettings->StoresCosts == 1): ?>
                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تكلفه المخازن')): ?>
                        <li>
                           <a href="<?php echo e(url('StoresCosts')); ?>" title="<?php echo e(trans('admin.StoresCosts')); ?>" data-filter-tags="<?php echo e(trans('admin.StoresCosts')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StoresCosts')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>



                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جرد المخازن')): ?>
                       <?php if($ReportsSettings->ItemCost == 1): ?>
                        <li>
                           <a href="<?php echo e(url('ItemCost')); ?>" title="<?php echo e(trans('admin.ItemCost')); ?>" data-filter-tags="<?php echo e(trans('admin.ItemCost')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ItemCost')); ?></span>
                           </a>
                        </li>
                           <?php endif; ?>

                        <?php endif; ?>


                                  <?php if($ReportsSettings->StoresMovesReport == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير حركات المخازن')): ?>
                                     <li>
                           <a href="<?php echo e(url('StoresMovesReport')); ?>" title="<?php echo e(trans('admin.StoresMovesReport')); ?>" data-filter-tags="<?php echo e(trans('admin.StoresMovesReport')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StoresMovesReport')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>



                         <?php if($ReportsSettings->ReportStartPeriodProducts == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير اصناف بدايه فتره')): ?>
                        <li>
                           <a href="<?php echo e(url('ReportStartPeriod')); ?>" title="<?php echo e(trans('admin.Product_Order_Limit')); ?>" data-filter-tags="<?php echo e(trans('admin.Product_Order_Limit')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ReportStartPeriodProducts')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                   <?php if($ReportsSettings->SettlementsReports == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقارير التسويات')): ?>
                        <li>
                           <a href="<?php echo e(url('SettlementsReports')); ?>" title="<?php echo e(trans('admin.SettlementsReports')); ?>" data-filter-tags="<?php echo e(trans('admin.SettlementsReports')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.SettlementsReports')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>


             <?php if($ReportsSettings->ExpiredProucts == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('صلاحيات الاصناف')): ?>
                        <li>
                           <a href="<?php echo e(url('ExpiredProucts')); ?>" title="<?php echo e(trans('admin.ExpiredProucts')); ?>" data-filter-tags="<?php echo e(trans('admin.ExpiredProucts')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ExpiredProucts')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>



                                   <?php if($ReportsSettings->Product_Order_Limit == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اصناف وصلت حد الطلب')): ?>
                        <li>
                           <a href="<?php echo e(url('ProductOrderLimit')); ?>" title="<?php echo e(trans('admin.Product_Order_Limit')); ?>" data-filter-tags="<?php echo e(trans('admin.Product_Order_Limit')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Product_Order_Limit')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>


                  <?php if($ReportsSettings->StoresTransferReport == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير تحويلات مخازن')): ?>
                                            <li>
                           <a href="<?php echo e(url('StoresTransferReport')); ?>" title="<?php echo e(trans('admin.StoresTransferReport')); ?>" data-filter-tags="<?php echo e(trans('admin.StoresTransferReport')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StoresTransferReport')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>
   <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير جرد السيريال')): ?>
                                                    <?php if($ReportsSettings->InventorySerial == 1): ?>
                         <li>
                           <a href="<?php echo e(url('InventorySerial')); ?>" title="<?php echo e(trans('admin.InventorySerial')); ?>" data-filter-tags="<?php echo e(trans('admin.InventorySerial')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.InventorySerial')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
        <?php endif; ?>





                     </div>

                     <div class="col-sm-3">



                         <?php if($ReportsSettings->SalesBills == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير فواتير المبيعات')): ?>
                        <li>
                           <a href="<?php echo e(url('SalesBills')); ?>" title="<?php echo e(trans('admin.SalesBills')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesBills')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.SalesBills')); ?></span>
                           </a>
                        </li>

                         <?php endif; ?>
                            <?php endif; ?>


                                       <?php if($ReportsSettings->GroupsSales == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات المجموعات')): ?>
                        <li>
                           <a href="<?php echo e(url('GroupsSales')); ?>" title="<?php echo e(trans('admin.GroupsSales')); ?>" data-filter-tags="<?php echo e(trans('admin.GroupsSales')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.GroupsSales')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>



                                    <?php if($ReportsSettings->ProfitGroupsReport == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير ربح مجموعات')): ?>
                                                            <li>
                           <a href="<?php echo e(url('ProfitGroupsReport')); ?>" title="<?php echo e(trans('admin.ProfitGroupsReport')); ?>" data-filter-tags="<?php echo e(trans('admin.ProfitGroupsReport')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ProfitGroupsReport')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>


                                   <?php if($ReportsSettings->SalesCustomersGroups == 1): ?>
                   <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات مجموعات عملاء تفصيلي')): ?>
                         <li>
                           <a href="<?php echo e(url('SalesCustomersGroups')); ?>" title="<?php echo e(trans('admin.SalesCustomersGroups')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesCustomersGroups')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.SalesCustomersGroups')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                        <?php endif; ?>

                                   <?php if($ReportsSettings->BrandsSales == 1): ?>
                                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات البرندات')): ?>
                               <li>
                           <a href="<?php echo e(url('BrandsSales')); ?>" title="<?php echo e(trans('admin.BrandsSales')); ?>" data-filter-tags="<?php echo e(trans('admin.BrandsSales')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.BrandsSales')); ?></span>
                           </a>
                        </li>
                               <?php endif; ?>
                         <?php endif; ?>



                                      <?php if($ReportsSettings->NetSales == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('صافي المبيعات')): ?>
                        <li>
                           <a href="<?php echo e(url('NetSales')); ?>" title="<?php echo e(trans('admin.NetSales')); ?>" data-filter-tags="<?php echo e(trans('admin.NetSales')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.NetSales')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>


                           <?php if($ReportsSettings->TotalNetSales == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اجمالي صافي المبيعات')): ?>
                        <li>
                           <a href="<?php echo e(url('TotalNetSales')); ?>" title="<?php echo e(trans('admin.TotalNetSales')); ?>" data-filter-tags="<?php echo e(trans('admin.TotalNetSales')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.TotalNetSales')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>


                                      <?php if($ReportsSettings->StoresSalesDetails == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات مخازن تفصيلي')): ?>
                        <li>
                           <a href="<?php echo e(url('StoresSalesDetails')); ?>" title=" <?php echo e(trans('admin.StoresSalesDetails')); ?>" data-filter-tags="<?php echo e(trans('admin.StoresSalesDetails')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StoresSalesDetails')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>


                                             <?php if($ReportsSettings->InstallmentCompaniesSales == 1): ?>
                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات شركات التقسيط')): ?>
                        <li>
                           <a href="<?php echo e(url('InstallmentCompaniesSales')); ?>" title="<?php echo e(trans('admin.InstallmentCompaniesSales')); ?>" data-filter-tags="<?php echo e(trans('admin.InstallmentCompaniesSales')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.InstallmentCompaniesSales')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>



                         <?php if($ReportsSettings->CompareSalesPrice == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير مقارنه اسعار بيع')): ?>

                                                        <li>
                           <a href="<?php echo e(url('CompareSalesPrice')); ?>" title="<?php echo e(trans('admin.CompareSalesPrice')); ?>" data-filter-tags="<?php echo e(trans('admin.CompareSalesPrice')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.CompareSalesPrice')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>





                         <?php if($ReportsSettings->PurchasesBills == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير فواتير المشتريات')): ?>
                             <li>
                           <a href="<?php echo e(url('PurchasesBills')); ?>" title="<?php echo e(trans('admin.PurchasesBills')); ?>" data-filter-tags="<?php echo e(trans('admin.PurchasesBills')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.PurchasesBills')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                            <?php endif; ?>



                                       <?php if($ReportsSettings->NetPurchases == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('صافي المشتريات')): ?>
                        <li>
                           <a href="<?php echo e(url('NetPurchases')); ?>" title="<?php echo e(trans('admin.NetPurchases')); ?>" data-filter-tags="<?php echo e(trans('admin.NetPurchases')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.NetPurchases')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>





                             <?php if($ReportsSettings->TotalNetPurchases == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اجمالي صافي المشتريات')): ?>
                        <li>
                           <a href="<?php echo e(url('TotalNetPurchases')); ?>" title="<?php echo e(trans('admin.TotalNetPurchases')); ?>" data-filter-tags="<?php echo e(trans('admin.TotalNetPurchases')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.TotalNetPurchases')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>




                                        <?php if($ReportsSettings->VendorPurchases == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مشتريات موردين')): ?>
                        <li>
                           <a href="<?php echo e(url('VendorPurchases')); ?>" title="<?php echo e(trans('admin.VendorPurchases')); ?>" data-filter-tags="<?php echo e(trans('admin.VendorPurchases')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.VendorPurchases')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>




                 <?php if($ReportsSettings->VendorPricesReport == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اسعار الموردين')): ?>
                        <li>
                           <a href="<?php echo e(url('VendorPricesReport')); ?>" title="<?php echo e(trans('admin.VendorPricesReport')); ?>" data-filter-tags="<?php echo e(trans('admin.VendorPricesReport')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.VendorPricesReport')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>






             </div>

                     <div class="col-sm-3">

                           <?php if($ReportsSettings->DailyClosing == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقفيل اليوميه')): ?>
                        <li>
                           <a href="<?php echo e(url('DailyClosing')); ?>" title="<?php echo e(trans('admin.DailyClosing')); ?>" data-filter-tags="<?php echo e(trans('admin.DailyClosing')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.DailyClosing')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                                      <?php if($ReportsSettings->DailyClosingDetails == 1): ?>
                       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقفيل اليوميه بتفاصيل')): ?>
                        <li>
                           <a href="<?php echo e(url('DailyClosingDetails')); ?>" title="<?php echo e(trans('admin.DailyClosingDetails')); ?>" data-filter-tags="<?php echo e(trans('admin.DailyClosingDetails')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.DailyClosingDetails')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                         <?php if($ReportsSettings->DailyMoves == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الحركات اليوميه')): ?>
                        <li>
                           <a href="<?php echo e(url('DailyMoves')); ?>" title="<?php echo e(trans('admin.DailyMoves')); ?>" data-filter-tags="<?php echo e(trans('admin.DailyMoves')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.DailyMoves')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>


                            <?php if($ReportsSettings->Profits == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الارباح')): ?>
                        <li>
                           <a href="<?php echo e(url('Profits')); ?>" title="<?php echo e(trans('admin.Profits')); ?>" data-filter-tags="<?php echo e(trans('admin.Profits')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Profits')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                                <?php if($ReportsSettings->Sales_Delegates == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات مناديب')): ?>
                        <li>
                           <a href="<?php echo e(url('Sales_Delegates')); ?>" title="<?php echo e(trans('admin.Sales_Delegates')); ?>" data-filter-tags="<?php echo e(trans('admin.Sales_Delegates')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Sales_Delegates')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                               <?php if($ReportsSettings->DelegateSalesDetailss == 1): ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات مناديب تفصيلي')): ?>
                        <li>
                           <a href="<?php echo e(url('DelegateSalesDetailss')); ?>" title="<?php echo e(trans('admin.DelegateSalesDetails')); ?>" data-filter-tags="<?php echo e(trans('admin.DelegateSalesDetails')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.DelegateSalesDetails')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                            <?php if($ReportsSettings->DelegateSalesDetails == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات مناديب تفصيلي')): ?>
                        <li>
                           <a href="<?php echo e(url('DelegateSalesDetails')); ?>" title=" <?php echo e(trans('admin.DelegateSalesDetails')); ?>" data-filter-tags="<?php echo e(trans('admin.DelegateSalesDetails')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.DelegateSalesDetails')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>



                            <?php if($ReportsSettings->ProfitDelegateSalesDetails == 1): ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ارباح مبيعات مناديب تفصيلي')): ?>
                        <li>
                           <a href="<?php echo e(url('ProfitDelegateSalesDetails')); ?>" title="<?php echo e(trans('admin.ProfitDelegateSalesDetails')); ?>" data-filter-tags="<?php echo e(trans('admin.ProfitDelegateSalesDetails')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ProfitDelegateSalesDetails')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                   <?php if($ReportsSettings->Collection_Delegates == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تحصيل مناديب')): ?>
                        <li>
                           <a href="<?php echo e(url('Collection_Delegates')); ?>" title="<?php echo e(trans('admin.Collection_Delegates')); ?>" data-filter-tags="<?php echo e(trans('admin.Collection_Delegates')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Collection_Delegates')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                                     <?php if($ReportsSettings->SalesProsMoreDetails == 1): ?>
                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير مبيعات مفصله')): ?>
                        <li>
                           <a href="<?php echo e(url('SalesProsMoreDetails')); ?>" title="<?php echo e(trans('admin.SalesProsMoreDetails')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesProsMoreDetails')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.SalesProsMoreDetails')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                                <?php if($ReportsSettings->ClientSales == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات العملاء')): ?>
                        <li>
                           <a href="<?php echo e(url('ClientSales')); ?>" title="<?php echo e(trans('admin.ClientSales')); ?>" data-filter-tags="<?php echo e(trans('admin.ClientSales')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ClientSales')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>



                           <?php if($ReportsSettings->ExecutorSales == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات المنفذ')): ?>
                        <li>
                           <a href="<?php echo e(url('ExecutorSales')); ?>" title="<?php echo e(trans('admin.ExecutorSales')); ?>" data-filter-tags="<?php echo e(trans('admin.ExecutorSales')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ExecutorSales')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>


                           <?php if($ReportsSettings->Shifts == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الورديات')): ?>
                        <li>
                           <a href="<?php echo e(url('ShiftsReport')); ?>" title="<?php echo e(trans('admin.Shifts')); ?>" data-filter-tags="<?php echo e(trans('admin.Shifts')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Shifts')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>
                          <?php if($ReportsSettings->Shifts_Details == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تفاصيل الورديات')): ?>
                        <li>
                           <a href="<?php echo e(url('ShiftsDetailsReport')); ?>" title="<?php echo e(trans('admin.Shifts_Details')); ?>" data-filter-tags="<?php echo e(trans('admin.Shifts_Details')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Shifts_Details')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>


                         <?php if($ReportsSettings->DailyShifts == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الورديات اليوميه')): ?>
                        <li>
                           <a href="<?php echo e(url('DailyShifts')); ?>" title="<?php echo e(trans('admin.DailyShifts')); ?>" data-filter-tags="<?php echo e(trans('admin.DailyShifts')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.DailyShifts')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>



             </div>

                  </div>
               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>
              <?php if($Modules->HR  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('HR')): ?>
            <li>


               <a href="#" title="<?php echo e(trans('admin.HR')); ?>" data-filter-tags="<?php echo e(trans('admin.HR')); ?>">
               <i class="fal fa-people-arrows"></i>
               <span class="nav-link-text" data-i18n="nav.plugins"><?php echo e(trans('admin.HR')); ?></span>
                   <input type="hidden" id="inpu9" value="1">
               </a>
               <ul style="width:450px"  id="menu9">
                  <div class="row">
                     <div class="col-sm-6">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اقسام العمل')): ?>
                        <li>
                           <a href="<?php echo e(url('WorkDepartments')); ?>" title=" <?php echo e(trans('admin.Work_Departments')); ?>" data-filter-tags="<?php echo e(trans('admin.Work_Departments')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_plugins_faq">
                           <?php echo e(trans('admin.Work_Departments')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مستويات التوظيف')): ?>
                        <li>
                           <a href="<?php echo e(url('Employment_levels')); ?>" title=" <?php echo e(trans('admin.Employment_levels')); ?>" data-filter-tags="<?php echo e(trans('admin.Employment_levels')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_plugins_faq">
                           <?php echo e(trans('admin.Employment_levels')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('شركات التأمين')): ?>
                        <li>
                           <a href="<?php echo e(url('Insurance_companies')); ?>" title=" <?php echo e(trans('admin.Insurance_companies')); ?>" data-filter-tags="<?php echo e(trans('admin.Insurance_companies')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_plugins_faq">
                           <?php echo e(trans('admin.Insurance_companies')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع الوظائف')): ?>
                        <li>
                           <a href="<?php echo e(url('Jobs_Type')); ?>" title="<?php echo e(trans('admin.Jobs_Type')); ?>" data-filter-tags="<?php echo e(trans('admin.Jobs_Type')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_waves"> <?php echo e(trans('admin.Jobs_Type')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه موظف')): ?>
                        <li>
                           <a href="<?php echo e(url('AddEmp')); ?>" title="<?php echo e(trans('admin.Add_Emp')); ?>" data-filter-tags="<?php echo e(trans('admin.Add_Emp')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_pacejs">  <?php echo e(trans('admin.Add_Emp')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('نقاط الانتاج')): ?>
                        <li>
                           <a href="<?php echo e(url('ProducationPoints')); ?>" title="<?php echo e(trans('admin.ProducationPoints')); ?>" data-filter-tags="<?php echo e(trans('admin.ProducationPoints')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_pacejs">  <?php echo e(trans('admin.ProducationPoints')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول الموظفين')): ?>
                        <li>
                           <a href="<?php echo e(url('EmpSechdule')); ?>" title="<?php echo e(trans('admin.Emp_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Emp_Sechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_smartpanels">
                           <?php echo e(trans('admin.Emp_Sechdule')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول طلبات توظيف')): ?>
                        <li>
                           <a href="<?php echo e(url('JobRequestsSechdule')); ?>" title="<?php echo e(trans('admin.JobRequestsSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.JobRequestsSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_smartpanels">
                           <?php echo e(trans('admin.JobRequestsSechdule')); ?>

                           </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع الاجازات')): ?>
                        <li>
                           <a href="<?php echo e(url('Holidays_Types')); ?>" title="<?php echo e(trans('admin.Holidays_Types')); ?>" data-filter-tags="<?php echo e(trans('admin.Holidays_Types')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_bootbox"><?php echo e(trans('admin.Holidays_Types')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع الاستحقاقات')): ?>
                        <li>
                           <a href="<?php echo e(url('Benefits_Types')); ?>" title="<?php echo e(trans('admin.Benefits_Types')); ?>" data-filter-tags="<?php echo e(trans('admin.Benefits_Types')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_slimscroll"><?php echo e(trans('admin.Benefits_Types')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع الاستقطاعات')): ?>
                        <li>
                           <a href="<?php echo e(url('Deductions_Types')); ?>" title="<?php echo e(trans('admin.Deductions_Types')); ?>" data-filter-tags="<?php echo e(trans('admin.Deductions_Types')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_throttle"> <?php echo e(trans('admin.Deductions_Types')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الساعات الاضافيه')): ?>
                        <li>
                           <a href="<?php echo e(url('Overtime')); ?>" title="<?php echo e(trans('admin.Overtime')); ?>" data-filter-tags="<?php echo e(trans('admin.Overtime')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_navigation"><?php echo e(trans('admin.Overtime')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع القروض')): ?>
                        <li>
                           <a href="<?php echo e(url('Loan_Types')); ?>" title="<?php echo e(trans('admin.Loan_Types')); ?>" data-filter-tags="<?php echo e(trans('admin.Loan_Types')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_navigation"><?php echo e(trans('admin.Loan_Types')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه سلفه')): ?>
                        <li>
                           <a href="<?php echo e(url('AddBorrow')); ?>" title="<?php echo e(trans('admin.AddBorrow')); ?>" data-filter-tags="<?php echo e(trans('admin.AddBorrow')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_navigation"><?php echo e(trans('admin.AddBorrow')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('السلف')): ?>
                        <li>
                           <a href="<?php echo e(url('Borrow')); ?>" title="<?php echo e(trans('admin.Borrow')); ?>" data-filter-tags="<?php echo e(trans('admin.Borrow')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_navigation"><?php echo e(trans('admin.Borrow')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('طلب اجازه')): ?>
                        <li>
                           <a href="<?php echo e(url('HolidaysOrder')); ?>" title="<?php echo e(trans('admin.HolidaysOrder')); ?>" data-filter-tags="<?php echo e(trans('admin.HolidaysOrder')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_i18next"><?php echo e(trans('admin.HolidaysOrder')); ?> </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الاجازات')): ?>
                        <li>
                           <a href="<?php echo e(url('Holidays')); ?>" title="<?php echo e(trans('admin.Holidays')); ?>" data-filter-tags="<?php echo e(trans('admin.Holidays')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_i18next"><?php echo e(trans('admin.Holidays')); ?> </span>
                           </a>
                        </li>
                        <?php endif; ?>
                     </div>
                     <div class="col-sm-6">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الاستحقاقات')): ?>
                        <li>
                           <a href="<?php echo e(url('Entitlements')); ?>" title="<?php echo e(trans('admin.Entitlements')); ?>" data-filter-tags="<?php echo e(trans('admin.Entitlements')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_navigation"><?php echo e(trans('admin.Entitlements')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الاستقطاعات')): ?>
                        <li>
                           <a href="<?php echo e(url('Deducation')); ?>" title="<?php echo e(trans('admin.Deducation')); ?>" data-filter-tags="<?php echo e(trans('admin.Deducation')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_navigation"><?php echo e(trans('admin.Deducation')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check(' الحضور')): ?>
                        <li>
                           <a href="<?php echo e(url('Attendance')); ?>" title="<?php echo e(trans('admin.Attendance')); ?>" data-filter-tags="<?php echo e(trans('admin.Attendance')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_i18next"> <?php echo e(trans('admin.Attendance')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول الحضور')): ?>
                        <li>
                           <a href="<?php echo e(url('AttendanceSechdule')); ?>" title="<?php echo e(trans('admin.AttendanceSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.AttendanceSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_i18next"> <?php echo e(trans('admin.AttendanceSechdule')); ?>    </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول الانصراف')): ?>
                        <li>
                           <a href="<?php echo e(url('DepartureSechdule')); ?>" title="<?php echo e(trans('admin.DepartureSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.DepartureSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_i18next"> <?php echo e(trans('admin.DepartureSechdule')); ?>    </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تسجيل الساعات الاضافيه')): ?>
                        <li>
                           <a href="<?php echo e(url('RegOverTime')); ?>" title="<?php echo e(trans('admin.Reg_OverTime')); ?>" data-filter-tags="<?php echo e(trans('admin.Reg_OverTime')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_i18next">  <?php echo e(trans('admin.Reg_OverTime')); ?> </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه قرض')): ?>
                        <li>
                           <a href="<?php echo e(url('AddLoan')); ?>" title="<?php echo e(trans('admin.AddLoan')); ?>" data-filter-tags="<?php echo e(trans('admin.AddLoan')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_i18next">  <?php echo e(trans('admin.AddLoan')); ?> </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('القروض')): ?>
                        <li>
                           <a href="<?php echo e(url('Loan')); ?>" title="<?php echo e(trans('admin.Loan')); ?> " data-filter-tags="<?php echo e(trans('admin.Loan')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_i18next">  <?php echo e(trans('admin.Loan')); ?> </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اقساط الموظفين')): ?>
                        <li>
                           <a href="<?php echo e(url('EmpInstallment')); ?>" title="<?php echo e(trans('admin.EmpInstallment')); ?>" data-filter-tags="<?php echo e(trans('admin.EmpInstallment')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_i18next">  <?php echo e(trans('admin.EmpInstallment')); ?> </span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('صرف راتب')): ?>
                        <li>
                           <a href="<?php echo e(url('AddSalary')); ?>" title="<?php echo e(trans('admin.AddSalary')); ?>" data-filter-tags="<?php echo e(trans('admin.AddSalary')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_app.core"> <?php echo e(trans('admin.AddSalary')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول الرواتب')): ?>
                        <li>
                           <a href="<?php echo e(url('SalarySechdules')); ?>" title="<?php echo e(trans('admin.SalarySechdules')); ?>" data-filter-tags="<?php echo e(trans('admin.SalarySechdules')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_app.core"> <?php echo e(trans('admin.SalarySechdules')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('صرف عمولات')): ?>
                        <li>
                           <a href="<?php echo e(url('ExchangeCommissions')); ?>" title="<?php echo e(trans('admin.ExchangeCommissions')); ?>" data-filter-tags="<?php echo e(trans('admin.ExchangeCommissions')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_app.core"> <?php echo e(trans('admin.ExchangeCommissions')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول صرف العمولات')): ?>
                        <li>
                           <a href="<?php echo e(url('ExchangeCommissionsSechdule')); ?>" title="<?php echo e(trans('admin.ExchangeCommissionsSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ExchangeCommissionsSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_app.core"> <?php echo e(trans('admin.ExchangeCommissionsSechdule')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>

                           <?php if(auth()->guard('admin')->user()->emp != 0): ?>

                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اهدافي')): ?>
                              <li>
                           <a href="<?php echo e(url('MyGoals')); ?>" title="<?php echo e(trans('admin.MyGoals')); ?>" data-filter-tags="<?php echo e(trans('admin.MyGoals')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_app.core"> <?php echo e(trans('admin.MyGoals')); ?></span>
                           </a>
                        </li>

                         <?php endif; ?>


                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('طلب استقاله')): ?>
                                  <li>
                           <a href="<?php echo e(url('ResignationRequest')); ?>" title="<?php echo e(trans('admin.ResignationRequest')); ?>" data-filter-tags="<?php echo e(trans('admin.ResignationRequest')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_app.core"> <?php echo e(trans('admin.ResignationRequest')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                               <?php endif; ?>

                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('طلبات استقاله')): ?>
                                    <li>
                           <a href="<?php echo e(url('ResignationRequestSechdule')); ?>" title="<?php echo e(trans('admin.ResignationRequestSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ResignationRequestSechdule')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_app.core"> <?php echo e(trans('admin.ResignationRequestSechdule')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>

                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اخلاء طرف')): ?>
                                  <li>
                           <a href="<?php echo e(url('Disclaimer')); ?>" title="<?php echo e(trans('admin.Disclaimer')); ?>" data-filter-tags="<?php echo e(trans('admin.Disclaimer')); ?>">
                           <span class="nav-link-text" data-i18n="nav.plugins_app.core"> <?php echo e(trans('admin.Disclaimer')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>

                     </div>
                  </div>
               </ul>
            </li>
            <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقارير الموارد البشريه')): ?>
              <li>


               <a href="#" title="<?php echo e(trans('admin.HR_Reports')); ?>" data-filter-tags="<?php echo e(trans('admin.HR_Reports')); ?>">
               <i class="fal fa-home"></i>
               <span class="nav-link-text" data-i18n="nav.form_stuff"><?php echo e(trans('admin.HR_Reports')); ?></span>
                   <input type="hidden" id="inpu10" value="1">
               </a>
               <ul style="width:450px"  id="menu10">

                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير الحضور و الانصراف')): ?>
                  <li>
                     <a href="<?php echo e(url('AttendenceAndDepartureReport')); ?>" title="<?php echo e(trans('admin.AttendenceAndDepartureReport')); ?>" data-filter-tags="<?php echo e(trans('admin.AttendenceAndDepartureReport')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.AttendenceAndDepartureReport')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير قيمة الحضور')): ?>
                  <li>
                     <a href="<?php echo e(url('AttendenceValueReport')); ?>" title="<?php echo e(trans('admin.AttendenceValueReport')); ?>" data-filter-tags="<?php echo e(trans('admin.AttendenceValueReport')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.AttendenceValueReport')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير صرف الرواتب')): ?>
                  <li>
                     <a href="<?php echo e(url('PaySalaryReport')); ?>" title="<?php echo e(trans('admin.PaySalaryReport')); ?>" data-filter-tags="<?php echo e(trans('admin.PaySalaryReport')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.PaySalaryReport')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                                               <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير الرواتب المسدده')): ?>
                  <li>
                     <a href="<?php echo e(url('SalaryPayed')); ?>" title="<?php echo e(trans('admin.SalaryPayed')); ?>" data-filter-tags="<?php echo e(trans('admin.SalaryPayed')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.SalaryPayed')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                                               <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير رواتب الموظفين')): ?>
                  <li>
                     <a href="<?php echo e(url('EmpSalaries')); ?>" title="<?php echo e(trans('admin.EmpSalaries')); ?>" data-filter-tags="<?php echo e(trans('admin.EmpSalaries')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.EmpSalaries')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                      <?php if($ReportsSettings->EmployeeCommissionDiscounts == 1): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('عمولات و خصومات الموظفين')): ?>
                        <li>
                           <a href="<?php echo e(url('EmployeeCommissionDiscounts')); ?>" title="<?php echo e(trans('admin.EmployeeCommissionDiscounts')); ?>" data-filter-tags="<?php echo e(trans('admin.EmployeeCommissionDiscounts')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.EmployeeCommissionDiscounts')); ?></span>
                           </a>
                        </li>
                        <?php endif; ?>
                         <?php endif; ?>

                          <?php if($ReportsSettings->EmpGoals == 1): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير اهداف الموظفين')): ?>

                                                                    <li>
                           <a href="<?php echo e(url('EmpGoals')); ?>" title="<?php echo e(trans('admin.EmpGoals')); ?>" data-filter-tags="<?php echo e(trans('admin.EmpGoals')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.EmpGoals')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                <?php endif; ?>



               </ul>
            </li>
                 <?php endif; ?>
             <?php endif; ?>
            <?php if($Modules->CRM  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اداره علاقه العملاء')): ?>
            <li>

               <a href="#" title="<?php echo e(trans('admin.CRM')); ?>" data-filter-tags="<?php echo e(trans('admin.CRM')); ?>">
               <i class="fal fa-people-carry"></i>
               <span class="nav-link-text" data-i18n="nav.form_stuff"><?php echo e(trans('admin.CRM')); ?></span>
                   <input type="hidden" id="inpu6" value="1">
               </a>
               <ul style="width:450px"  id="menu6">
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الدول')): ?>
                  <li>
                     <a href="<?php echo e(url('Countris')); ?>" title="<?php echo e(trans('admin.Countris')); ?>" data-filter-tags="<?php echo e(trans('admin.Countris')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Countris')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المحافظه')): ?>
                  <li>
                     <a href="<?php echo e(url('Governrate')); ?>" title="<?php echo e(trans('admin.Governrate')); ?>" data-filter-tags="<?php echo e(trans('admin.Governrate')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Governrate')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('النشاطات')): ?>
                  <li>
                     <a href="<?php echo e(url('Activites')); ?>" title="<?php echo e(trans('admin.Activites')); ?>" data-filter-tags="<?php echo e(trans('admin.Activites')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Activites')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حالات العملاء')): ?>
                  <li>
                     <a href="<?php echo e(url('Clients_Status')); ?>" title="<?php echo e(trans('admin.Clients_Status')); ?>" data-filter-tags="<?php echo e(trans('admin.Clients_Status')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Clients_Status')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المنصات')): ?>
                  <li>
                     <a href="<?php echo e(url('Platforms')); ?>" title="<?php echo e(trans('admin.Platforms')); ?>" data-filter-tags="<?php echo e(trans('admin.Platforms')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Platforms')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع المقابلات')): ?>
                  <li>
                     <a href="<?php echo e(url('Interviews_Types')); ?>" title="<?php echo e(trans('admin.Interviews_Types')); ?>" data-filter-tags="<?php echo e(trans('admin.Interviews_Types')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Interviews_Types')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المقابلات')): ?>
                  <li>
                     <a href="<?php echo e(url('Interviews')); ?>" title="<?php echo e(trans('admin.Interviews')); ?>" data-filter-tags="<?php echo e(trans('admin.Interviews')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Interviews')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المشاريع')): ?>
                  <li>
                     <a href="<?php echo e(url('Projects')); ?>" title="<?php echo e(trans('admin.Projects')); ?>" data-filter-tags="<?php echo e(trans('admin.Projects')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Projects')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المهام')): ?>
                  <li>
                     <a href="<?php echo e(url('Missions')); ?>" title="<?php echo e(trans('admin.Missions')); ?>" data-filter-tags="<?php echo e(trans('admin.Missions')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Missions')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المنافسين')): ?>
                  <li>
                     <a href="<?php echo e(url('Competitors')); ?>" title="<?php echo e(trans('admin.Competitors')); ?>" data-filter-tags="<?php echo e(trans('admin.Competitors')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Competitors')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>






                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مهامي')): ?>
                  <?php if(auth()->guard('admin')->user()->emp != 0): ?>
                  <li>
                     <a href="<?php echo e(url('MyMissions')); ?>" title="<?php echo e(trans('admin.MyMissions')); ?>" data-filter-tags="<?php echo e(trans('admin.MyMissions')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.MyMissions')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مقابلاتي')): ?>
                  <?php if(auth()->guard('admin')->user()->emp != 0): ?>
                  <li>
                     <a href="<?php echo e(url('MyMettings')); ?>" title="<?php echo e(trans('admin.MyMettings')); ?>" data-filter-tags="<?php echo e(trans('admin.MyMettings')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.MyMettings')); ?></span>
                     </a>
                  </li>
                  <li>
                     <a href="<?php echo e(url('PerivousMettings')); ?>" title="<?php echo e(trans('admin.PerivousMettings')); ?>" data-filter-tags="<?php echo e(trans('admin.PerivousMettings')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.PerivousMettings')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php endif; ?>
               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>
            <?php if($Modules->Bill_Electronic  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الفاتوره الالكترونيه')): ?>
            <li>


               <a href="#" title="<?php echo e(trans('admin.Bill_Electronic')); ?>" data-filter-tags="<?php echo e(trans('admin.Bill_Electronic')); ?>">
               <i class="fal fa-scroll"></i>
               <span class="nav-link-text" data-i18n="nav.form_stuff"><?php echo e(trans('admin.Bill_Electronic')); ?></span>
                   <input type="hidden" id="inpu7" value="1">
               </a>
               <ul style="width:450px"  id="menu7">
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ارسال فواتير مبيعات')): ?>
                  <li>
                     <a href="<?php echo e(url('Send_Bill_Sales')); ?>" title="<?php echo e(trans('admin.Send_Bill_Sales')); ?>" data-filter-tags="<?php echo e(trans('admin.Send_Bill_Sales')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Send_Bill_Sales')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ارسال فواتير مرتجعات مبيعات')): ?>
                      <li>
                     <a href="<?php echo e(url('Send_Bill_ReturnSales')); ?>" title="<?php echo e(trans('admin.Send_Bill_ReturnSales')); ?>" data-filter-tags="<?php echo e(trans('admin.Send_Bill_ReturnSales')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Send_Bill_ReturnSales')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>

                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('فواتير مرتجعات مرسله')): ?>
                 <!-- <li>
                     <a href="<?php echo e(url('Bill_Purchases_Sent')); ?>" title="<?php echo e(trans('admin.Bill_Purchases_Sent')); ?>" data-filter-tags="utilities borders">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Bill_Purchases_Sent')); ?></span>
                     </a>
                  </li> -->

                      <li>
                     <a href="<?php echo e(url('Bill_ReturnSales_Sent')); ?>" title="<?php echo e(trans('admin.Bill_ReturnSales_Sent')); ?>" data-filter-tags="<?php echo e(trans('admin.Bill_ReturnSales_Sent')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Bill_ReturnSales_Sent')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('فواتير مبيعات مرسله')): ?>
                  <li>
                     <a href="<?php echo e(url('Bill_Sales_Sent')); ?>" title="<?php echo e(trans('admin.Bill_Sales_Sent')); ?>" data-filter-tags="<?php echo e(trans('admin.Bill_Sales_Sent')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Bill_Sales_Sent')); ?></span>
                     </a>
                  </li>
                     <li>
                     <a href="<?php echo e(url('Bill_Sales_Sent_Web')); ?>" title="<?php echo e(trans('admin.Bill_Sales_Sent_Web')); ?>" data-filter-tags="<?php echo e(trans('admin.Bill_Sales_Sent_Web')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Bill_Sales_Sent_Web')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ارسال ايصال الكتروني')): ?>
                  <li>
                     <a href="<?php echo e(url('Send_Recipt_Sales')); ?>" title="<?php echo e(trans('admin.Send_Recipt_Sales')); ?>" data-filter-tags="<?php echo e(trans('admin.Send_Recipt_Sales')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Send_Recipt_Sales')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ارسال مرتجع ايصال الكتروني')): ?>
                  <li>
                     <a href="<?php echo e(url('ReturnSend_Recipt_Sales')); ?>" title="<?php echo e(trans('admin.ReturnSend_Recipt_Sales')); ?>" data-filter-tags="<?php echo e(trans('admin.ReturnSend_Recipt_Sales')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ReturnSend_Recipt_Sales')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ايصالات مرسله')): ?>
                        <li>
                     <a href="<?php echo e(url('Recipt_Sales_Sent')); ?>" title="<?php echo e(trans('admin.Recipt_Sales_Sent')); ?>" data-filter-tags="<?php echo e(trans('admin.Recipt_Sales_Sent')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Recipt_Sales_Sent')); ?></span>
                     </a>
                  </li>
          <?php endif; ?>
               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>
               <?php if($Modules->Resturant  ==  1): ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المطاعم')): ?>
              <li>


               <a href="#" title="<?php echo e(trans('admin.Resturant')); ?>" data-filter-tags="<?php echo e(trans('admin.Resturant')); ?>">
               <i class="fal fa-home"></i>
               <span class="nav-link-text" data-i18n="nav.form_stuff"><?php echo e(trans('admin.Resturant')); ?></span>
                   <input type="hidden" id="inpu22" value="1">
               </a>
               <ul style="width:450px"  id="menu22">

                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الطاولات')): ?>
                  <li>
                     <a href="<?php echo e(url('Tables')); ?>" title="<?php echo e(trans('admin.Tables')); ?>" data-filter-tags="<?php echo e(trans('admin.Tables')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Tables')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>



                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('شاشة المطبخ')): ?>
                  <li>
                     <a href="<?php echo e(url('KitchenScreen')); ?>" title="<?php echo e(trans('admin.KitchenScreen')); ?>" data-filter-tags="<?php echo e(trans('admin.KitchenScreen')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.KitchenScreen')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>



                 <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('شاشة التسليمات')): ?>
                  <li>
                     <a href="<?php echo e(url('RecivedScreen')); ?>" title="<?php echo e(trans('admin.RecivedScreen')); ?>" data-filter-tags="<?php echo e(trans('admin.RecivedScreen')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.RecivedScreen')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>



                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات المطعم')): ?>
                   <?php if(auth()->guard('admin')->user()->emp != 0): ?>
                  <li>
                     <a href="<?php echo e(url('ResturantSales')); ?>" title="<?php echo e(trans('admin.ResturantSales')); ?>" data-filter-tags="<?php echo e(trans('admin.ResturantSales')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ResturantSales')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>
                  <?php endif; ?>




                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('سلايدر المطاعم')): ?>
                        <li>
                     <a href="<?php echo e(url('RWebSlider')); ?>" title="<?php echo e(trans('admin.WebSlider')); ?>" data-filter-tags="<?php echo e(trans('admin.WebSlider')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.WebSlider')); ?>  (Home 1)</span>
                     </a>
                  </li>
                   <?php endif; ?>


                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('هوم ٢ المطاعم')): ?>
                                                <li>
                     <a href="<?php echo e(url('ResturantHome')); ?>" title="<?php echo e(trans('admin.Images')); ?>" data-filter-tags="<?php echo e(trans('admin.Images')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"><?php echo e(trans('admin.Images')); ?>  (Home 2) </span>
                     </a>
                  </li>
                   <?php endif; ?>

                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('من نحن المطاعم')): ?>
                                                <li>
                     <a href="<?php echo e(url('RAbout')); ?>" title="<?php echo e(trans('admin.About')); ?>" data-filter-tags="<?php echo e(trans('admin.About')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.About')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>


                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('قسم الفيديو المطاعم')): ?>

                                                <li>
                     <a href="<?php echo e(url('VideoSection')); ?>" title="<?php echo e(trans('admin.VideoSection')); ?>" data-filter-tags="<?php echo e(trans('admin.VideoSection')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.VideoSection')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>

                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تواصل اجتماعي المطاعم')): ?>
                        <li>
                     <a href="<?php echo e(url('RSocialMedia')); ?>" title="<?php echo e(trans('admin.SocialMedia')); ?>" data-filter-tags="<?php echo e(trans('admin.SocialMedia')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.SocialMedia')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>

                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المقالات المطاعم')): ?>
                        <li>
                     <a href="<?php echo e(url('RArticles')); ?>" title="<?php echo e(trans('admin.Articles')); ?>" data-filter-tags="<?php echo e(trans('admin.Articles')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Articles')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>


                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('سياسه الخصوصيه المطاعم')): ?>
                        <li>
                     <a href="<?php echo e(url('RPolices')); ?>" title="<?php echo e(trans('admin.Polices')); ?>" data-filter-tags="<?php echo e(trans('admin.Polices')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Polices')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>


                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('شروط و احكام المطاعم')): ?>
                        <li>
                     <a href="<?php echo e(url('RTerms')); ?>" title="<?php echo e(trans('admin.Terms')); ?>" data-filter-tags="<?php echo e(trans('admin.Terms')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Terms')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>


                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('كود خصم المطاعم')): ?>

                                           <li>
                     <a href="<?php echo e(url('RCouponCode')); ?>" title="<?php echo e(trans('admin.CouponCode')); ?>" data-filter-tags="<?php echo e(trans('admin.CouponCode')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.CouponCode')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>

                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الاستوديو المطاعم')): ?>
                                           <li>
                     <a href="<?php echo e(url('RGallery')); ?>" title="<?php echo e(trans('admin.Gallery')); ?>" data-filter-tags="<?php echo e(trans('admin.Gallery')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Gallery')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>

                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مراجعات المطاعم')): ?>

                                           <li>
                     <a href="<?php echo e(url('RReviews')); ?>" title="<?php echo e(trans('admin.Reviews')); ?>" data-filter-tags="<?php echo e(trans('admin.Reviews')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Reviews')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>



                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حجوزات المطاعم')): ?>
                                           <li>
                     <a href="<?php echo e(url('RReservations')); ?>" title="<?php echo e(trans('admin.Reservations')); ?>" data-filter-tags="<?php echo e(trans('admin.Reservations')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Reservations')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>



                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('طلبات المطعم')): ?>
                      <li>
                     <a href="<?php echo e(url('ResturantOrders')); ?>" title="<?php echo e(trans('admin.Orders')); ?>" data-filter-tags="<?php echo e(trans('admin.Orders')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Orders')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                         <?php if(in_array(auth()->guard('admin')->user()->email, ['<EMAIL>', '<EMAIL>', '<EMAIL>'])): ?>

                                                  <li>
                     <a href="<?php echo e(url('ResturantStyle')); ?>" title="<?php echo e(trans('admin.ResturantStyle')); ?>" data-filter-tags="<?php echo e(trans('admin.ResturantStyle')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ResturantStyle')); ?></span>
                     </a>
                  </li>

                   <?php endif; ?>


               </ul>
            </li>
            <?php endif; ?>
             <?php endif; ?>
            <?php if($Modules->Hotels  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الفنادق')): ?>
            <li>


               <a href="#" title="<?php echo e(trans('admin.Hotels')); ?>" data-filter-tags="<?php echo e(trans('admin.Hotels')); ?>">
               <i class="fal fa-home"></i>
               <span class="nav-link-text" data-i18n="nav.form_stuff"><?php echo e(trans('admin.Hotels')); ?></span>
                   <input type="hidden" id="inpu8" value="1">
               </a>
               <ul style="width:450px"  id="menu8">
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع الغرف')): ?>
                  <li>
                     <a href="<?php echo e(url('RoomsType')); ?>" title="<?php echo e(trans('admin.RoomsType')); ?>" data-filter-tags="<?php echo e(trans('admin.RoomsType')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.RoomsType')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الغرف')): ?>
                  <li>
                     <a href="<?php echo e(url('Rooms')); ?>" title="<?php echo e(trans('admin.Rooms')); ?>" data-filter-tags="<?php echo e(trans('admin.Rooms')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Rooms')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الحجز')): ?>
                  <li>
                     <a href="<?php echo e(url('Reservations')); ?>" title="<?php echo e(trans('admin.Reservations')); ?>" data-filter-tags="<?php echo e(trans('admin.Reservations')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Reservations')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول الحجوزات')): ?>
                  <li>
                     <a href="<?php echo e(url('Reservations_Sechdule')); ?>" title="<?php echo e(trans('admin.Reservations_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Reservations_Sechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Reservations_Sechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقرير الحجوزات')): ?>
                  <li>
                     <a href="<?php echo e(url('ReservationsReport')); ?>" title="<?php echo e(trans('admin.ReservationsReport')); ?>" data-filter-tags="<?php echo e(trans('admin.ReservationsReport')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ReservationsReport')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>
            <?php if($Modules->Manufacturing  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('التصنيع')): ?>
            <li>

               <a href="#" title="<?php echo e(trans('admin.Manufacturing')); ?>" data-filter-tags="<?php echo e(trans('admin.Manufacturing')); ?>">
               <i class="fal fa-cogs"></i>
               <span class="nav-link-text" data-i18n="nav.miscellaneous"> <?php echo e(trans('admin.Manufacturing')); ?>   </span>
                   <input type="hidden" id="inpu11" value="1">
               </a>
               <ul style="width:450px"  id="menu11">
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('صالات التصنيع')): ?>
                  <li>
                     <a href="<?php echo e(url('ManufacturingHalls')); ?>" title="<?php echo e(trans('admin.ManufacturingHalls')); ?>" data-filter-tags="<?php echo e(trans('admin.ManufacturingHalls')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ManufacturingHalls')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('نموذج التصنيع')): ?>
                  <li>
                     <a href="<?php echo e(url('ManufacturingModel')); ?>" title="<?php echo e(trans('admin.ManufacturingModel')); ?>" data-filter-tags="<?php echo e(trans('admin.ManufacturingModel')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ManufacturingModel')); ?> </span>
                     </a>
                  </li>
                  <li>
                     <a href="<?php echo e(url('ManufacturingModelPrecent')); ?>" title="<?php echo e(trans('admin.ManufacturingModelPrecent')); ?>" data-filter-tags="<?php echo e(trans('admin.ManufacturingModelPrecent')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ManufacturingModelPrecent')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if($DefManu->Manu_Type  == 1): ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('طلب تصنيع')): ?>
                  <li>
                     <a href="<?php echo e(url('Manufacturing_Request')); ?>" title="<?php echo e(trans('admin.Manufacturing_Request')); ?>" data-filter-tags="<?php echo e(trans('admin.Manufacturing_Request')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.Manufacturing_Request')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول طلبات التصنيع')): ?>
                  <li>
                     <a href="<?php echo e(url('Manufacturing_Request_Sechdule')); ?>" title="<?php echo e(trans('admin.Manufacturing_Request_Sechdule')); ?> " data-filter-tags="<?php echo e(trans('admin.Manufacturing_Request_Sechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.Manufacturing_Request_Sechdule')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('آمر تصنيع')): ?>
                  <li>
                     <a href="<?php echo e(url('ManufacturingOrder')); ?>" title="<?php echo e(trans('admin.ManufacturingOrder')); ?>" data-filter-tags="<?php echo e(trans('admin.ManufacturingOrder')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ManufacturingOrder')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول اوامر التصنيع')): ?>
                  <li>
                     <a href="<?php echo e(url('ManufacturingOrderSechdule')); ?>" title="<?php echo e(trans('admin.ManufacturingOrderSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ManufacturingOrderSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ManufacturingOrderSechdule')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول اذن صرف بضاعه تصنيع')): ?>
                  <li>
                     <a href="<?php echo e(url('ExchangeManufacturingGoodsSechdule')); ?>" title="<?php echo e(trans('admin.ExchangeManufacturingGoodsSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ExchangeManufacturingGoodsSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ExchangeManufacturingGoodsSechdule')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول تنفيذ التصنيع')): ?>
                  <li>
                     <a href="<?php echo e(url('ManufacturingExecutionSechdule')); ?>" title="<?php echo e(trans('admin.ManufacturingExecutionSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ManufacturingExecutionSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ManufacturingExecutionSechdule')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع الفحوصات')): ?>
                  <li>
                     <a href="<?php echo e(url('ExaminationsTypes')); ?>" title="<?php echo e(trans('admin.ExaminationsTypes')); ?>" data-filter-tags="<?php echo e(trans('admin.ExaminationsTypes')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ExaminationsTypes')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول الجوده')): ?>
                  <li>
                     <a href="<?php echo e(url('QualitySechdule')); ?>" title="<?php echo e(trans('admin.QualitySechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.QualitySechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.QualitySechdule')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول نماذج التصنيع')): ?>
                  <li>
                     <a href="<?php echo e(url('ManufacturingModelSechdule')); ?>" title="<?php echo e(trans('admin.ManufacturingModelSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ManufacturingModelSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ManufacturingModelSechdule')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('التنفيذ و الاستلام')): ?>
                  <li>
                     <a href="<?php echo e(url('ExecutingandReceiving')); ?>" title="<?php echo e(trans('admin.ExecutingandReceiving')); ?>" data-filter-tags="<?php echo e(trans('admin.ExecutingandReceiving')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ExecutingandReceiving')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>

            <?php if($Modules->Petrol  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('البنزينه')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.Petrol')); ?>" data-filter-tags="<?php echo e(trans('admin.Petrol')); ?>">
               <i class="fal fa-tachometer-alt-fastest"></i>
               <span class="nav-link-text" data-i18n="nav.miscellaneous"> <?php echo e(trans('admin.Petrol')); ?>   </span>
                   <input type="hidden" id="inpu13" value="1">
               </a>
               <ul style="width:450px"  id="menu13">
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('سيارات الشركه')): ?>
                  <li>
                     <a href="<?php echo e(url('CompanyCars')); ?>" title="<?php echo e(trans('admin.CompanyCars')); ?>" data-filter-tags="<?php echo e(trans('admin.CompanyCars')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_clearfix"> <?php echo e(trans('admin.CompanyCars')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع البونات')): ?>
                  <li>
                     <a href="<?php echo e(url('BonesType')); ?>" title="<?php echo e(trans('admin.BonesType')); ?>" data-filter-tags="<?php echo e(trans('admin.BonesType')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_clearfix"> <?php echo e(trans('admin.BonesType')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع الايصالات')): ?>
                  <li>
                     <a href="<?php echo e(url('ReciptsType')); ?>" title="<?php echo e(trans('admin.ReciptsType')); ?>" data-filter-tags="<?php echo e(trans('admin.ReciptsType')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_clearfix"> <?php echo e(trans('admin.ReciptsType')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع العدادات')): ?>
                  <li>
                     <a href="<?php echo e(url('CountersType')); ?>" title="<?php echo e(trans('admin.CountersType')); ?>" data-filter-tags="<?php echo e(trans('admin.CountersType')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_clearfix"> <?php echo e(trans('admin.CountersType')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مشتريات الوقود')): ?>
                  <li>
                     <a href="<?php echo e(url('PurchasePetrol')); ?>" title="<?php echo e(trans('admin.PurchasePetrol')); ?>" data-filter-tags="<?php echo e(trans('admin.PurchasePetrol')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_clearfix"> <?php echo e(trans('admin.PurchasePetrol')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول مشتريات الوقود')): ?>
                  <li>
                     <a href="<?php echo e(url('PurchasePetrolSechdule')); ?>" title="<?php echo e(trans('admin.PurchasePetrolSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.PurchasePetrolSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_clearfix"> <?php echo e(trans('admin.PurchasePetrolSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات الوقود')): ?>
                  <li>
                     <a href="<?php echo e(url('SalesPetrol')); ?>" title="<?php echo e(trans('admin.SalesPetrol')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesPetrol')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_clearfix"> <?php echo e(trans('admin.SalesPetrol')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول مبيعات الوقود')): ?>
                  <li>
                     <a href="<?php echo e(url('SalesPetrolSechdule')); ?>" title="<?php echo e(trans('admin.SalesPetrolSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.SalesPetrolSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_clearfix"> <?php echo e(trans('admin.SalesPetrolSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>
            <?php if($Modules->Maintenance  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الصيانه')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.Maintenance')); ?>" data-filter-tags="<?php echo e(trans('admin.Maintenance')); ?>">
               <i class="fal fa-user-cog"></i>
               <span class="nav-link-text" data-i18n="nav.form_stuff"><?php echo e(trans('admin.Maintenance')); ?></span>
                   <input type="hidden" id="inpu14" value="1">
               </a>
               <ul style="width:450px"  id="menu14">
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الشركه المصنعه')): ?>
                  <li>
                     <a href="<?php echo e(url('ManufacturCompany')); ?>" title="<?php echo e(trans('admin.ManufacturCompany')); ?>" data-filter-tags="<?php echo e(trans('admin.ManufacturCompany')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ManufacturCompany')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اسباب رفض الصيانه')): ?>
                  <li>
                     <a href="<?php echo e(url('RefuseReasons')); ?>" title="<?php echo e(trans('admin.RefuseReasons')); ?>" data-filter-tags="<?php echo e(trans('admin.RefuseReasons')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.RefuseReasons')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حالات الاجهزه')): ?>
                  <li>
                     <a href="<?php echo e(url('DesviceCases')); ?>" title="<?php echo e(trans('admin.DesviceCases')); ?>" data-filter-tags="<?php echo e(trans('admin.DesviceCases')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.DesviceCases')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اوصاف الاجهزه')): ?>
                  <li>
                     <a href="<?php echo e(url('DeviceDescrips')); ?>" title="<?php echo e(trans('admin.DeviceDescrips')); ?>" data-filter-tags="<?php echo e(trans('admin.DeviceDescrips')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.DeviceDescrips')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('شروط و احكام الصيانه')): ?>
                  <li>
                     <a href="<?php echo e(url('TermsMaintaince')); ?>" title="<?php echo e(trans('admin.TermsMaintaince')); ?>" data-filter-tags="<?php echo e(trans('admin.TermsMaintaince')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.TermsMaintaince')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الوان الصيانه')): ?>
                  <li>
                     <a href="<?php echo e(url('MaintainceColors')); ?>" title="<?php echo e(trans('admin.MaintainceColors')); ?>" data-filter-tags="<?php echo e(trans('admin.MaintainceColors')); ?>">
                     <span class="nav-link-text" data-i18n="nav.ui_components_buttons"><?php echo e(trans('admin.MaintainceColors')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <!--
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع الاعطال')): ?>
                               <li>
                           <a href="<?php echo e(url('FaultsType')); ?>" title="<?php echo e(trans('admin.FaultsType')); ?>" data-filter-tags="utilities borders">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.FaultsType')); ?></span>
                           </a>
                        </li>
                         <?php endif; ?>
                         -->
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('سند استلام صيانه')): ?>
                  <li>
                     <a href="<?php echo e(url('ReciptMaintaince')); ?>" title=" <?php echo e(trans('admin.ReciptMaintaince')); ?>" data-filter-tags="<?php echo e(trans('admin.ReciptMaintaince')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ReciptMaintaince')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول سند استلام صيانه')): ?>
                  <li>
                     <a href="<?php echo e(url('ReciptMaintainceSechdule')); ?>" title="<?php echo e(trans('admin.ReciptMaintainceSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ReciptMaintainceSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ReciptMaintainceSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('فاتوره صيانه')): ?>
                  <!--         <li>
                     <a href="<?php echo e(url('MaintainceBill')); ?>" title="Borders" data-filter-tags="utilities borders">
                                  <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.MaintainceBill')); ?></span>
                                  </a>
                               </li>  -->
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول فواتير الصيانه')): ?>
                  <li>
                     <a href="<?php echo e(url('MaintainceBillSechdule')); ?>" title="<?php echo e(trans('admin.MaintainceBillSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.MaintainceBillSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.MaintainceBillSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول مرتجع الصيانه')): ?>
                  <li>
                     <a href="<?php echo e(url('ReturnMaintainceBillSechdule')); ?>" title="<?php echo e(trans('admin.ReturnMaintainceBillSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ReturnMaintainceBillSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ReturnMaintainceBillSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if(auth()->guard('admin')->user()->emp != 0): ?>
                  <li>
                     <a href="<?php echo e(url('ReciptMaintainceSechduleEng')); ?>" title="<?php echo e(trans('admin.ReciptMaintainceSechduleEng')); ?>" data-filter-tags="<?php echo e(trans('admin.ReciptMaintainceSechduleEng')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ReciptMaintainceSechduleEng')); ?><span style="color: #e980d5;padding-left: 3px;"><?php echo e($CountRecipt); ?></span></span>
                     </a>
                  </li>
                  <?php endif; ?>


                     <?php if($ReportsSettings->MaintanceSalesReport == 1): ?>

                                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات الصيانه')): ?>
                               <li>
                           <a href="<?php echo e(url('MaintanceSalesReport')); ?>" title="<?php echo e(trans('admin.MaintanceSalesReport')); ?>" data-filter-tags="<?php echo e(trans('admin.MaintanceSalesReport')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.MaintanceSalesReport')); ?></span>
                           </a>
                        </li>
                               <?php endif; ?>
                         <?php endif; ?>

                            <?php if($ReportsSettings->Maintenance_Tune == 1): ?>

                                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('توالف الصيانه')): ?>
                               <li>
                           <a href="<?php echo e(url('Maintenance_Tune')); ?>" title="<?php echo e(trans('admin.Maintenance_Tune')); ?>" data-filter-tags="<?php echo e(trans('admin.Maintenance_Tune')); ?>">
                           <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Maintenance_Tune')); ?></span>
                           </a>
                        </li>
                               <?php endif; ?>
                         <?php endif; ?>

               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>
            <?php if($Modules->Secretariat  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الامانات')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.Secretariat')); ?>" data-filter-tags="<?php echo e(trans('admin.Secretariat')); ?>">
              <i class="fal fa-user-lock"></i>
               <span class="nav-link-text" data-i18n="nav.form_stuff"><?php echo e(trans('admin.Secretariat')); ?></span>
                <input type="hidden" id="inpu15" value="1">
               </a>
               <ul style="width:450px"  id="menu15">
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مخازن الامانات')): ?>
                  <li>
                     <a href="<?php echo e(url('Secretariat_Stores')); ?>" title="<?php echo e(trans('admin.Secretariat_Stores')); ?>" data-filter-tags="<?php echo e(trans('admin.Secretariat_Stores')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Secretariat_Stores')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('وارد بضاعه الامانات')): ?>
                  <li>
                     <a href="<?php echo e(url('Secretariat_Import_goods')); ?>" title="<?php echo e(trans('admin.Secretariat_Import_goods')); ?>" data-filter-tags="<?php echo e(trans('admin.Secretariat_Import_goods')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Secretariat_Import_goods')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول وارد بضاعه الامانات')): ?>
                  <li>
                     <a href="<?php echo e(url('Secretariat_Import_goods_Sechdule')); ?>" title="<?php echo e(trans('admin.Secretariat_Import_goods_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Secretariat_Import_goods_Sechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Secretariat_Import_goods_Sechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('صرف بضاعه امانات')): ?>
                  <li>
                     <a href="<?php echo e(url('Secretariat_Export_goods')); ?>" title="<?php echo e(trans('admin.Secretariat_Export_goods')); ?>" data-filter-tags="<?php echo e(trans('admin.Secretariat_Export_goods')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Secretariat_Export_goods')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول صرف بضاعه الامانات')): ?>
                  <li>
                     <a href="<?php echo e(url('Secretariat_Export_goods_Sechdule')); ?>" title="<?php echo e(trans('admin.Secretariat_Export_goods_Sechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.Secretariat_Export_goods_Sechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Secretariat_Export_goods_Sechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('نموذج تصنيع للغير')): ?>
                  <li>
                     <a href="<?php echo e(url('ManufacturingModelSecretariat')); ?>" title="<?php echo e(trans('admin.ManufacturingModelSecretariat')); ?>" data-filter-tags="<?php echo e(trans('admin.ManufacturingModelSecretariat')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ManufacturingModelSecretariat')); ?> </span>
                     </a>
                  </li>

                               <li>
                     <a href="<?php echo e(url('ManufacturingModelSecretariatPrecent')); ?>" title="<?php echo e(trans('admin.ManufacturingModelSecretariatPrecent')); ?>" data-filter-tags="<?php echo e(trans('admin.ManufacturingModelSecretariatPrecent')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ManufacturingModelSecretariatPrecent')); ?> </span>
                     </a>
                  </li>


                  <?php endif; ?>

                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول نماذج تصنيع للغير')): ?>
                  <li>
              <a href="<?php echo e(url('ManufacturingModelSecretariatSechdule')); ?>" title="<?php echo e(trans('admin.ManufacturingModelSecretariatSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ManufacturingModelSecretariatSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ManufacturingModelSecretariatSechdule')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>

                                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تنفيذ و استلام للغير')): ?>
                  <li>
              <a href="<?php echo e(url('ExecutingReceivingSecretariat')); ?>" title="<?php echo e(trans('admin.ExecutingReceivingSecretariat')); ?>" data-filter-tags="<?php echo e(trans('admin.ExecutingReceivingSecretariat')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.ExecutingReceivingSecretariat')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>



                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('كميات مخازن الامانات')): ?>
                  <li>
                     <a href="<?php echo e(url('Secretariat_Stores_Qty')); ?>" title="<?php echo e(trans('admin.Secretariat_Stores_Qty')); ?>" data-filter-tags="<?php echo e(trans('admin.Secretariat_Stores_Qty')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Secretariat_Stores_Qty')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('طلباتي')): ?>
            <?php if(auth()->guard('admin')->user()->ship != 0): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.Orders')); ?>" data-filter-tags="<?php echo e(trans('admin.Orders')); ?>">
               <i class="fal fa-edit"></i>
               <span class="nav-link-text" data-i18n="nav.form_stuff"><?php echo e(trans('admin.Orders')); ?></span>
                <input type="hidden" id="inpu16" value="1">
               </a>
               <ul style="width:450px"  id="menu16">
                  <li>
                     <a href="<?php echo e(url('Orders')); ?>" title="<?php echo e(trans('admin.Orders')); ?>" data-filter-tags="<?php echo e(trans('admin.Orders')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Orders')); ?></span>
                     </a>
                  </li>
               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>
            <?php if($Modules->ECommerce  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الموقع')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.Website')); ?>" data-filter-tags="<?php echo e(trans('admin.Website')); ?>">
               <i class="fal fa-globe"></i>
               <span class="nav-link-text" data-i18n="nav.form_stuff"><?php echo e(trans('admin.Website')); ?></span>
                   <input type="hidden" id="inpu17" value="1">
               </a>
               <ul style="width:450px"  id="menu17">
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('سلايدر الموقع')): ?>
                  <li>
                     <a href="<?php echo e(url('WebSlider')); ?>" title="<?php echo e(trans('admin.WebSlider')); ?>" data-filter-tags="<?php echo e(trans('admin.WebSlider')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.WebSlider')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('من نحن')): ?>
                  <li>
                     <a href="<?php echo e(url('About')); ?>" title="<?php echo e(trans('admin.About')); ?>" data-filter-tags="<?php echo e(trans('admin.About')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.About')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('التواصل الاجتماعي')): ?>
                  <li>
                     <a href="<?php echo e(url('SocialMedia')); ?>" title="<?php echo e(trans('admin.SocialMedia')); ?>" data-filter-tags="<?php echo e(trans('admin.SocialMedia')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.SocialMedia')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('رسايل التواصل')): ?>
                  <li>
                     <a href="<?php echo e(url('MsgRqst')); ?>" title="<?php echo e(trans('admin.MsgRqst')); ?>" data-filter-tags="<?php echo e(trans('admin.MsgRqst')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.MsgRqst')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('التواصل معنا')): ?>
                  <li>
                     <a href="<?php echo e(url('ContactUS')); ?>" title="<?php echo e(trans('admin.ContactUS')); ?>" data-filter-tags="<?php echo e(trans('admin.ContactUS')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ContactUS')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المقالات')): ?>
                  <li>
                     <a href="<?php echo e(url('Articles')); ?>" title="<?php echo e(trans('admin.Articles')); ?>" data-filter-tags="<?php echo e(trans('admin.Articles')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Articles')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('السياسات')): ?>
                  <li>
                     <a href="<?php echo e(url('Polices')); ?>" title="<?php echo e(trans('admin.Polices')); ?>" data-filter-tags="<?php echo e(trans('admin.Polices')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Polices')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الشروط و الاحكام')): ?>
                  <li>
                     <a href="<?php echo e(url('Terms')); ?>" title="<?php echo e(trans('admin.Terms')); ?>" data-filter-tags="<?php echo e(trans('admin.Terms')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Terms')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('كوبون كود')): ?>
                  <li>
                     <a href="<?php echo e(url('CouponCode')); ?>" title=" <?php echo e(trans('admin.CouponCode')); ?>" data-filter-tags="<?php echo e(trans('admin.CouponCode')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.CouponCode')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الاسئله الشائعه')): ?>
                  <li>
                     <a href="<?php echo e(url('FAQ')); ?>" title="<?php echo e(trans('admin.FAQ')); ?>" data-filter-tags="<?php echo e(trans('admin.FAQ')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.FAQ')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المميزات')): ?>
                  <li>
                     <a href="<?php echo e(url('Features')); ?>" title="<?php echo e(trans('admin.Features')); ?>" data-filter-tags="<?php echo e(trans('admin.Features')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Features')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('لماذا تختارنا')): ?>
                  <li>
                     <a href="<?php echo e(url('HowWeWork')); ?>" title="<?php echo e(trans('admin.WhyChoose')); ?>" data-filter-tags="<?php echo e(trans('admin.WhyChoose')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.WhyChoose')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اعلانات تفاصيل االمنتج')): ?>
                  <li>
                     <a href="<?php echo e(url('ProDetailsImg')); ?>" title="<?php echo e(trans('admin.ProDetailsImg')); ?>" data-filter-tags="<?php echo e(trans('admin.ProDetailsImg')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ProDetailsImg')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('صور سياسه المتجر')): ?>
                  <li>
                     <a href="<?php echo e(url('BefroeFooter')); ?>" title=" <?php echo e(trans('admin.BefroeFooter')); ?>" data-filter-tags="<?php echo e(trans('admin.BefroeFooter')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.BefroeFooter')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('طلبات المتجر')): ?>
                  <li>
                     <a href="<?php echo e(url('ShopOrders')); ?>" title="<?php echo e(trans('admin.Orders')); ?>" data-filter-tags="<?php echo e(trans('admin.Orders')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Orders')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('معرض الصور')): ?>
                  <li>
                     <a href="<?php echo e(url('Gallery')); ?>" title="<?php echo e(trans('admin.Gallery')); ?>" data-filter-tags="<?php echo e(trans('admin.Gallery')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Gallery')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الخدمات')): ?>
                  <li>
                     <a href="<?php echo e(url('Services')); ?>" title="<?php echo e(trans('admin.Services')); ?>" data-filter-tags="<?php echo e(trans('admin.Services')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Services')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المدونة')): ?>
                  <li>
                     <a href="<?php echo e(url('Blog')); ?>" title="<?php echo e(trans('admin.Blog')); ?>" data-filter-tags="<?php echo e(trans('admin.Blog')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Blog')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('فريق العمل')): ?>
                  <li>
                     <a href="<?php echo e(url('Team')); ?>" title="<?php echo e(trans('admin.Team')); ?>" data-filter-tags="<?php echo e(trans('admin.Team')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Team')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('آراء العملاء')): ?>
                  <li>
                     <a href="<?php echo e(url('Testimonials')); ?>" title="<?php echo e(trans('admin.Testimonials')); ?>" data-filter-tags="<?php echo e(trans('admin.Testimonials')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Testimonials')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المميزات الإضافية')): ?>
                  <li>
                     <a href="<?php echo e(url('WebsiteFeatures')); ?>" title="<?php echo e(trans('admin.Website_Features')); ?>" data-filter-tags="<?php echo e(trans('admin.Website_Features')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Website_Features')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                   <?php if(in_array(auth()->guard('admin')->user()->email, ['<EMAIL>', '<EMAIL>', '<EMAIL>'])): ?>

                         <li>
                     <a href="<?php echo e(url('EComDesign')); ?>" title="<?php echo e(trans('admin.EComDesign')); ?>" data-filter-tags="<?php echo e(trans('admin.EComDesign')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.EComDesign')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>

               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>
            <?php if($Modules->Shipping  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الشحن')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.Shipping')); ?>" data-filter-tags="<?php echo e(trans('admin.Shipping')); ?>">
              <i class="fal fa-truck"></i>
               <span class="nav-link-text" data-i18n="nav.miscellaneous"> <?php echo e(trans('admin.Shipping')); ?>   </span>
                   <input type="hidden" id="inpu18" value="1">
               </a>
               <ul style="width:450px"  id="menu18">
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع الشحنات')): ?>
                  <li>
                     <a href="<?php echo e(url('ShippingType')); ?>" title="<?php echo e(trans('admin.ShippingType')); ?>" data-filter-tags="<?php echo e(trans('admin.ShippingType')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ShippingType')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حالات الشحنات')): ?>
                  <li>
                     <a href="<?php echo e(url('ShippingStatus')); ?>" title="<?php echo e(trans('admin.ShippingStatus')); ?>" data-filter-tags="<?php echo e(trans('admin.ShippingStatus')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ShippingStatus')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('امر الشحن')): ?>
                  <li>
                     <a href="<?php echo e(url('ShippingOrder')); ?>" title="<?php echo e(trans('admin.ShippingOrder')); ?>" data-filter-tags="<?php echo e(trans('admin.ShippingOrder')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ShippingOrder')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('طلباتي شحن')): ?>
                  <?php if(auth()->guard('admin')->user()->emp != 0): ?>
                  <li>
                     <a href="<?php echo e(url('MyOrdersEmp')); ?>" title="<?php echo e(trans('admin.MyOrders')); ?>" data-filter-tags="<?php echo e(trans('admin.MyOrders')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.MyOrders')); ?></span>
                     </a>
                  </li>
                  <li>
                     <a href="<?php echo e(url('ReportMyOrdersEmp')); ?>" title="<?php echo e(trans('admin.ReportMyOrders')); ?>" data-filter-tags="<?php echo e(trans('admin.ReportMyOrders')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ReportMyOrders')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تحصيل مورد')): ?>
                  <li>
                     <a href="<?php echo e(url('VendorCoolections')); ?>" title="<?php echo e(trans('admin.VendorCollections')); ?>" data-filter-tags="<?php echo e(trans('admin.VendorCollections')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.VendorCollections')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تقارير الطلبات')): ?>
                  <li>
                     <a href="<?php echo e(url('ReportOrders')); ?>" title="<?php echo e(trans('admin.ReportOrders')); ?>" data-filter-tags="<?php echo e(trans('admin.ReportOrders')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ReportOrders')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('البوليصه')): ?>
                  <li>
                     <a href="<?php echo e(url('Tickets')); ?>" title="<?php echo e(trans('admin.Tickets')); ?>" data-filter-tags="<?php echo e(trans('admin.Tickets')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Tickets')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول البوليصات')): ?>
                  <li>
                     <a href="<?php echo e(url('TicketsSechdule')); ?>" title="<?php echo e(trans('admin.TicketsSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.TicketsSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.TicketsSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('قائمه الشحن')): ?>
                  <li>
                     <a href="<?php echo e(url('ShippingList')); ?>" title="<?php echo e(trans('admin.ShippingList')); ?>" data-filter-tags="<?php echo e(trans('admin.ShippingList')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ShippingList')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                               <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول قوائم شحن')): ?>
                  <li>
                     <a href="<?php echo e(url('ShippingListSechdule')); ?>" title="<?php echo e(trans('admin.ShippingListSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ShippingListSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ShippingListSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                                         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('استلام شحنات')): ?>
                  <li>
                     <a href="<?php echo e(url('ShipmentReceipts')); ?>" title="<?php echo e(trans('admin.ShipmentReceipts')); ?>" data-filter-tags="<?php echo e(trans('admin.ShipmentReceipts')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ShipmentReceipts')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                                                 <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول استلام شحنات')): ?>
                  <li>
                     <a href="<?php echo e(url('ShipmentReceiptsSechdule')); ?>" title="<?php echo e(trans('admin.ShipmentReceiptsSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ShipmentReceiptsSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ShipmentReceiptsSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                                                      <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('استلام شحنات عملاء')): ?>
                  <li>
                     <a href="<?php echo e(url('ShipmentReceiptsClients')); ?>" title="<?php echo e(trans('admin.ShipmentReceiptsClients')); ?>" data-filter-tags="<?php echo e(trans('admin.ShipmentReceiptsClients')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ShipmentReceiptsClients')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول استلام شحنات عملاء')): ?>
                  <li>
                     <a href="<?php echo e(url('ShipmentReceiptsClientsSechdule')); ?>" title="<?php echo e(trans('admin.ShipmentReceiptsClientsSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ShipmentReceiptsClientsSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ShipmentReceiptsClientsSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>


         <?php if($Modules->Traning_Center  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مراكز التدريب')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.TrainingCenters')); ?>" data-filter-tags="<?php echo e(trans('admin.TrainingCenters')); ?>">
              <i class="fal fa-truck"></i>
               <span class="nav-link-text" data-i18n="nav.miscellaneous"> <?php echo e(trans('admin.TrainingCenters')); ?>   </span>
                   <input type="hidden" id="inpu25" value="1">
               </a>
               <ul style="width:450px"  id="menu25">
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('فئات الكورسات')): ?>
                  <li>
                     <a href="<?php echo e(url('CoursesCategory')); ?>" title="<?php echo e(trans('admin.CoursesCategory')); ?>" data-filter-tags="<?php echo e(trans('admin.CoursesCategory')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.CoursesCategory')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المواد العلمية')): ?>
                  <li>
                     <a href="<?php echo e(url('ScientificMaterial')); ?>" title="<?php echo e(trans('admin.ScientificMaterial')); ?>" data-filter-tags="<?php echo e(trans('admin.ScientificMaterial')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ScientificMaterial')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مجموعة الطلاب')): ?>
                  <li>
                     <a href="<?php echo e(url('StudentGroup')); ?>" title="<?php echo e(trans('admin.StudentGroup')); ?>" data-filter-tags="<?php echo e(trans('admin.StudentGroup')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StudentGroup')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('انواع الكورسات')): ?>
                  <li>
                     <a href="<?php echo e(url('CoursesType')); ?>" title="<?php echo e(trans('admin.CoursesType')); ?>" data-filter-tags="<?php echo e(trans('admin.CoursesType')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.CoursesType')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                                               <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الحالات الخاصة')): ?>
                  <li>
                     <a href="<?php echo e(url('SpecialCases')); ?>" title="<?php echo e(trans('admin.SpecialCases')); ?>" data-filter-tags="<?php echo e(trans('admin.SpecialCases')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.SpecialCases')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                                                       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('القاعات')): ?>
                  <li>
                     <a href="<?php echo e(url('CoursesHalls')); ?>" title="<?php echo e(trans('admin.CoursesHalls')); ?>" data-filter-tags="<?php echo e(trans('admin.CoursesHalls')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.CoursesHalls')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                   <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المدربين')): ?>
                  <li>
                     <a href="<?php echo e(url('Teachers')); ?>" title="<?php echo e(trans('admin.Teachers')); ?>" data-filter-tags="<?php echo e(trans('admin.Teachers')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Teachers')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                                      <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول المدربين')): ?>
                  <li>
                     <a href="<?php echo e(url('TeachersSechdule')); ?>" title="<?php echo e(trans('admin.TeachersSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.TeachersSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.TeachersSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                             <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الطلاب')): ?>
                  <li>
                     <a href="<?php echo e(url('Students')); ?>" title="<?php echo e(trans('admin.Students')); ?>" data-filter-tags="<?php echo e(trans('admin.Students')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Students')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول الطلاب')): ?>
                  <li>
                     <a href="<?php echo e(url('StudentsSechdule')); ?>" title="<?php echo e(trans('admin.StudentsSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.StudentsSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.StudentsSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                   <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الكورسات')): ?>
                  <li>
                     <a href="<?php echo e(url('Courses')); ?>" title="<?php echo e(trans('admin.Courses')); ?>" data-filter-tags="<?php echo e(trans('admin.Courses')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Courses')); ?></span>
                     </a>
                  </li>
                   <li>
                     <a href="<?php echo e(url('CoursesDetails')); ?>" title="<?php echo e(trans('admin.CoursesDetails')); ?>" data-filter-tags="<?php echo e(trans('admin.CoursesDetails')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.CoursesDetails')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حجز كورس')): ?>
                  <li>
                     <a href="<?php echo e(url('ReserveCourse')); ?>" title="<?php echo e(trans('admin.ReserveCourse')); ?>" data-filter-tags="<?php echo e(trans('admin.ReserveCourse')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ReserveCourse')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                                       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول حجوزات الكورسات')): ?>
                  <li>
                     <a href="<?php echo e(url('ReserveCourseSechdule')); ?>" title="<?php echo e(trans('admin.ReserveCourseSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.ReserveCourseSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ReserveCourseSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تسجيل حضور كورسات')): ?>
                  <li>
                     <a href="<?php echo e(url('RegCourses')); ?>" title="<?php echo e(trans('admin.RegCourses')); ?>" data-filter-tags="<?php echo e(trans('admin.RegCourses')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.RegCourses')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                               <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول تسجيل حضور كورسات')): ?>
                  <li>
                     <a href="<?php echo e(url('RegCoursesSechdule')); ?>" title="<?php echo e(trans('admin.RegCoursesSechdule')); ?>" data-filter-tags="<?php echo e(trans('admin.RegCoursesSechdule')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.RegCoursesSechdule')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>




         <?php if($Modules->Translate  ==  1): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الترجمة')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.Translate')); ?>" data-filter-tags="<?php echo e(trans('admin.Translate')); ?>">
              <i class="fal fa-truck"></i>
               <span class="nav-link-text" data-i18n="nav.miscellaneous"> <?php echo e(trans('admin.Translate')); ?>   </span>
                   <input type="hidden" id="inpu26" value="1">
               </a>
               <ul style="width:450px"  id="menu26">


                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الغرض من السفر')): ?>
                  <li>
                     <a href="<?php echo e(url('PurposeTravel')); ?>" title="<?php echo e(trans('admin.PurposeTravel')); ?>" data-filter-tags="<?php echo e(trans('admin.PurposeTravel')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.PurposeTravel')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                   <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اللغات')): ?>
                  <li>
                     <a href="<?php echo e(url('Languages')); ?>" title="<?php echo e(trans('admin.Languages')); ?>" data-filter-tags="<?php echo e(trans('admin.Languages')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Languages')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                   <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('السفارات')): ?>
                  <li>
                     <a href="<?php echo e(url('Empassies')); ?>" title="<?php echo e(trans('admin.Empassies')); ?>" data-filter-tags="<?php echo e(trans('admin.Empassies')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Empassies')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('شركات الترجمة و السياحة')): ?>
                  <li>
                     <a href="<?php echo e(url('TranslationTourismCompanies')); ?>" title="<?php echo e(trans('admin.TranslationTourismCompanies')); ?>" data-filter-tags="<?php echo e(trans('admin.TranslationTourismCompanies')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.TranslationTourismCompanies')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('نماذج الترجمة')): ?>
                  <li>
                     <a href="<?php echo e(url('TranslteModules')); ?>" title="<?php echo e(trans('admin.TranslteModules')); ?>" data-filter-tags="<?php echo e(trans('admin.TranslteModules')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.TranslteModules')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حجز موعد سفارة')): ?>
                  <li>
                     <a href="<?php echo e(url('EmpassyReserveDate')); ?>" title="<?php echo e(trans('admin.EmpassyReserveDate')); ?>" data-filter-tags="<?php echo e(trans('admin.EmpassyReserveDate')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.EmpassyReserveDate')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>


                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافة ترجمة')): ?>
                  <li>
                     <a href="<?php echo e(url('AddTranslate')); ?>" title="<?php echo e(trans('admin.AddTranslate')); ?>" data-filter-tags="<?php echo e(trans('admin.AddTranslate')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.AddTranslate')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>






               </ul>
            </li>
            <?php endif; ?>
            <?php endif; ?>




               <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الاعدادات')): ?>
            <li>
               <a href="#" title="<?php echo e(trans('admin.Settings')); ?>" data-filter-tags="<?php echo e(trans('admin.Settings')); ?>">
               <i class="fal fa-wrench"></i>
               <span class="nav-link-text" data-i18n="nav.miscellaneous"> <?php echo e(trans('admin.Settings')); ?>   </span>
                   <input type="hidden" id="inpu12" value="1">
               </a>
               <ul style="width:450px"  id="menu12">
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('صلاحيات المستخدمين')): ?>
                  <li>
                     <a href="<?php echo e(url('AdminsPremations')); ?>" title="<?php echo e(trans('admin.AdminsPremations')); ?>" data-filter-tags="<?php echo e(trans('admin.AdminsPremations')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.AdminsPremations')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('المستخدمين')): ?>
                  <li>
                     <a href="<?php echo e(url('Admins')); ?>" title="<?php echo e(trans('admin.Admins')); ?>" data-filter-tags="<?php echo e(trans('admin.Admins')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.Admins')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('البيانات الافتراضيه')): ?>
                  <li>
                     <a href="<?php echo e(url('Default_Data')); ?>" title="<?php echo e(trans('admin.Default_Data')); ?>" data-filter-tags="<?php echo e(trans('admin.Default_Data')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.Default_Data')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if(in_array(auth()->guard('admin')->user()->email, ['<EMAIL>', '<EMAIL>', '<EMAIL>'])): ?>
                  <li>
                     <a href="<?php echo e(url('Modules_Settings')); ?>" title="<?php echo e(trans('admin.Modules_Settings')); ?>" data-filter-tags="<?php echo e(trans('admin.Modules_Settings')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.Modules_Settings')); ?> </span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if(in_array(auth()->guard('admin')->user()->email, ['<EMAIL>', '<EMAIL>', '<EMAIL>'])): ?>
                  <li>
                     <a href="<?php echo e(url('QR')); ?>" title="QR" data-filter-tags="QR">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> QR</span>
                     </a>
                  </li>
                        <li>
                     <a href="<?php echo e(url('LoginSlider')); ?>" title="Login Slider" data-filter-tags="Login Slider">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> Login Slider</span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حركه المستخدمين')): ?>
                  <li>
                     <a href="<?php echo e(url('UserLog')); ?>" title="<?php echo e(trans('admin.User_Log')); ?>" data-filter-tags="<?php echo e(trans('admin.User_Log')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.User_Log')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مواقع الموظفين')): ?>
                  <li>
                     <a href="<?php echo e(url('EmpLocations')); ?>" title="<?php echo e(trans('admin.Emp_Locations')); ?>" data-filter-tags="<?php echo e(trans('admin.Emp_Locations')); ?>">
                     <span class="nav-link-text" data-i18n="nav.miscellaneous_fullcalendar"> <?php echo e(trans('admin.Emp_Locations')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('استيراد ملفات اكسيل')): ?>
                  <li>
                     <a href="<?php echo e(url('ExportProducts')); ?>" title="<?php echo e(trans('admin.ExportProducts')); ?>" data-filter-tags="<?php echo e(trans('admin.ExportProducts')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.ExportProducts')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الترجمه')): ?>
                  <li>
                     <a href="<?php echo e(url('Translate')); ?>" title="<?php echo e(trans('admin.Translate')); ?>" data-filter-tags="<?php echo e(trans('admin.Translate')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Translate')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>
                       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حذف الحركات')): ?>
                  <li>
                     <a href="<?php echo e(url('DeleteMoves')); ?>" title="<?php echo e(trans('admin.DeleteMoves')); ?>" data-filter-tags="<?php echo e(trans('admin.DeleteMoves')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.DeleteMoves')); ?></span>
                     </a>
                  </li>
                  <?php endif; ?>



                   <?php if(in_array(auth()->guard('admin')->user()->email, ['<EMAIL>', '<EMAIL>', '<EMAIL>'])): ?>
                          <li>
                     <a href="<?php echo e(url('Domain_Regstration')); ?>" title="<?php echo e(trans('admin.Domain_Regstration')); ?>" data-filter-tags="<?php echo e(trans('admin.Domain_Regstration')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Domain_Regstration')); ?></span>
                     </a>
                  </li>


                         <li>
                     <a href="<?php echo e(url('Updates')); ?>" title="<?php echo e(trans('admin.Updates')); ?>" data-filter-tags="<?php echo e(trans('admin.Updates')); ?>">
                     <span class="nav-link-text" data-i18n="nav.utilities_borders"> <?php echo e(trans('admin.Updates')); ?></span>
                     </a>
                  </li>
                   <?php endif; ?>

               </ul>
            </li>
            <?php endif; ?>



            <?php endif; ?>



            <li>
            </li>
         </ul>
         <div class="filter-message js-filter-message bg-success-600"></div>
      </nav>
      <!-- END PRIMARY NAVIGATION -->
      <!-- NAV FOOTER -->
      <div class="nav-footer shadow-top">
         <a href="#" onclick="return false;" data-action="toggle" data-class="nav-function-minify" class="hidden-md-down">
         <i class="ni ni-chevron-right"></i>
         <i class="ni ni-chevron-right"></i>
         </a>
         <ul class="list-table m-auto nav-footer-buttons">
            <li>
               <a target="_blank" href="https://www.facebook.com/ost" data-toggle="tooltip" data-placement="top" title="Chat logs">
               <i class="fab fa-facebook-messenger"></i>
               </a>
            </li>
            <li>
               <a target="_blank" href="https://wa.me/201284993268" data-toggle="tooltip" data-placement="top" title="Whatsapp">
               <i class="fab fa-whatsapp"></i>
               </a>
            </li>
            <li>
               <a target="_blank" href="00201284993268" data-toggle="tooltip" data-placement="top" title="00201284993268">
               <i class="fal fa-phone"></i>
               </a>
            </li>
         </ul>
      </div>
      <!-- END NAV FOOTER -->
   </aside>
   <!-- END Left Aside -->
   <div class="page-content-wrapper">
   <!-- BEGIN Page Header -->
   <header class="page-header" role="banner">
      <!-- we need this logo when user switches to nav-function-top -->
      <div class="page-logo">
         <a href="#" class="page-logo-link press-scale-down d-flex align-items-center position-relative" data-toggle="modal" data-target="#modal-shortcut">
         <?php if(!empty($Def->Logo)): ?>
         <img src="<?php echo e(URL::to($Def->Logo)); ?>" alt="SmartAdmin WebApp" aria-roledescription="logo">
         <?php else: ?>
         <img src="<?php echo e(asset('Admin/img/logo.png')); ?>" alt="SmartAdmin WebApp" aria-roledescription="logo">
         <?php endif; ?>
         <span class="page-logo-text mr-1">
            <?php echo e(app()->getLocale() == 'ar' ?$Def->Name :$Def->NameEn); ?>

         </span>
         <span class="position-absolute text-white opacity-50 small pos-top pos-right mr-2 mt-n2"></span>
         <i class="fal fa-angle-down d-inline-block ml-1 fs-lg color-primary-300"></i>
         </a>
      </div>
      <!-- DOC: nav menu layout change shortcut -->
      <div class="hidden-md-down dropdown-icon-menu position-relative">
         <a href="#" class="header-btn btn js-waves-off" data-action="toggle" data-class="nav-function-hidden" title="Hide Navigation">
         <i class="ni ni-menu"></i>
         </a>
         <ul>
            <li>
               <a href="#" class="btn js-waves-off" data-action="toggle" data-class="nav-function-minify" title="Minify Navigation">
               <i class="ni ni-minify-nav"></i>
               </a>
            </li>
            <li>
               <a href="#" class="btn js-waves-off" data-action="toggle" data-class="nav-function-fixed" title="Lock Navigation">
               <i class="ni ni-lock-nav"></i>
               </a>
            </li>
         </ul>
      </div>
      <!-- DOC: mobile button appears during mobile width -->
      <div class="hidden-lg-up">
         <a href="#" class="header-btn btn press-scale-down" data-action="toggle" data-class="mobile-nav-on">
         <i class="ni ni-menu"></i>
         </a>
      </div>
      <!--<div class="search">-->
      <!--   <form class="app-forms hidden-xs-down" role="search" action="page_search.html" autocomplete="off">-->
      <!--      <input type="text" id="search-field" placeholder="Search for anything" class="form-control" tabindex="1">-->
      <!--      <a href="#" onclick="return false;" class="btn-danger btn-search-close js-waves-off d-none" data-action="toggle" data-class="mobile-search-on">-->
      <!--      <i class="fal fa-times"></i>-->
      <!--      </a>-->
      <!--   </form>-->
      <!--</div>-->


    <?php if($ModulesSett->System == 1): ?>
     <?php if(!in_array(auth()->guard('admin')->user()->email, ['<EMAIL>', '<EMAIL>', '<EMAIL>'])): ?>
       <?php

       $day7=date("Y-m-d", strtotime("-7 days", strtotime($ModulesSett->Expire_Date)));
       $day6=date("Y-m-d", strtotime("-6 days", strtotime($ModulesSett->Expire_Date)));
       $day5=date("Y-m-d", strtotime("-5 days", strtotime($ModulesSett->Expire_Date)));
       $day4=date("Y-m-d", strtotime("-4 days", strtotime($ModulesSett->Expire_Date)));
       $day3=date("Y-m-d", strtotime("-3 days", strtotime($ModulesSett->Expire_Date)));
       $day2=date("Y-m-d", strtotime("-2 days", strtotime($ModulesSett->Expire_Date)));
       $day1=date("Y-m-d", strtotime("-1 days", strtotime($ModulesSett->Expire_Date)));
       ?>

<?php if(date('Y-m-d') == $day7 or date('Y-m-d')  == $day6 or date('Y-m-d')  == $day5 or date('Y-m-d')  == $day4 or date('Y-m-d')  == $day3 or date('Y-m-d')  == $day2 or date('Y-m-d')  == $day1): ?>


                           <div class='line text-center ml-3 mr-3'>
    <h6 class='pop-outin'> <?php echo e(trans('admin.Renew_Date_Soon')); ?> <?php echo e($ModulesSett->Expire_Date); ?></h6>
  </div>
       <?php endif; ?>
       <?php endif; ?>
       <?php endif; ?>


       <?php    $Events=Event::where('Event_En_Name','Expired Supscribe')->get();     ?>

                <?php $__currentLoopData = $Events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                <?php if(date('Y-m-d') ==  $event->End_Date): ?>

  <?php    $pro=Products::find($event->Product);     ?>
                      <div class='line text-center ml-3 mr-3'>
    <h6 class='pop-outin'>
    <?php echo e(trans('admin.Expired_Subscribe')); ?>

        <?php if(!empty($pro)): ?>
       ( <?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?>  )

        <?php endif; ?>


        <?php echo e($event->End_Date); ?>

            </h6>
  </div>

            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>




       <style>


/* crops animations that exceeds one line area */
.line {
  width: 100%;
  height: 4rem;
  overflow: hidden;
  padding: 5px;
  margin-bottom: 16px;
}

/* subtle zoom to attention and then back */
.pop-outin {
  animation: 2s anim-popoutin ease infinite;
        padding: 17px;

}

@keyframes  anim-popoutin {
  0% {
    color: black;
    transform: scale(0);
    opacity: 0;
    text-shadow: 0 0 0 rgba(0, 0, 0, 0);
  }
  25% {
    color: red;
    transform: scale(2);
    opacity: 1;
    text-shadow: 3px 10px 5px rgba(0, 0, 0, 0.5);
  }
  50% {
    color: black;
    transform: scale(1);
    opacity: 1;
    text-shadow: 1px 0 0 rgba(0, 0, 0, 0);
  }
  100% {
    /* animate nothing to add pause at the end of animation */
    transform: scale(1);
    opacity: 1;
    text-shadow: 1px 0 0 rgba(0, 0, 0, 0);
  }
}

.fromtop {
  animation: 2s anim-fromtop linear infinite;
}
@keyframes  anim-fromtop {
  0% { opacity: 0; transform: translateY(-100%);}
  25% { opacity: 1; transform: translateY(0%);}
  50% {
  }
  100% {
  }
}
       </style>

      <div class="top-left-nav d-flex">
         <!-- activate app search icon (mobile) -->
         <!--<div class="hidden-sm-up">-->
         <!--   <a href="#" class="header-icon" data-action="toggle" data-class="mobile-search-on" data-focus="search-field" title="Search">-->
         <!--   <i class="fal fa-search"></i>-->
         <!--   </a>-->
         <!--</div>-->
         <!-- app settings -->
         <div class="hidden-md-down">
            <a href="#" class="header-icon" data-toggle="modal" data-target=".js-modal-settings">
            <i class="fal fa-cog"></i>
            </a>
         </div>
      <?php if(auth()->guard('admin')->user()->vend == 0 and  auth()->guard('admin')->user()->cli == 0 and  auth()->guard('admin')->user()->emp == 0): ?>
         <!-- app shortcuts -->
         <div>
            <a href="#" class="header-icon" data-toggle="dropdown" title="My Apps">
            <i class="fal fa-cube"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-animated w-auto h-auto">
               <div class="dropdown-header bg-trans-gradient d-flex justify-content-center align-items-center rounded-top">
                  <h4 class="m-0 text-center color-white">
                     <?php echo e(trans('admin.Quick_Shortcut')); ?>

                  </h4>
               </div>
               <div class="custom-scroll h-100">
                  <?php if($Modules->Stores  ==  1): ?>
                  <ul class="app-list">
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه عملاء')): ?>
                     <li>
                        <a href="<?php echo e(url('AddClients')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/013.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Add_Clients')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه  مبيعات')): ?>
                     <li>
                        <a href="<?php echo e(url('Sales')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/019.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Sales')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول المبيعات')): ?>
                     <li>
                        <a href="<?php echo e(url('SalesSechdule')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/020.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.SalesSechdule')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه سند قبض')): ?>
                     <li>
                        <a href="<?php echo e(url('Receipt_Voucher')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/003.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Receipt_Voucher')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه سند صرف')): ?>
                     <li>
                        <a href="<?php echo e(url('Payment_Voucher')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/004.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Payment_Voucher')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تحويلات الخزائن')): ?>
                     <li>
                        <a href="<?php echo e(url('SafesTransfer')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/005.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Safes_Transfer')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول تحويلات الخزائن')): ?>
                     <li>
                        <a href="<?php echo e(url('SafesTransferSechdule')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/006.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Safes_Transfer_Sechdule')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه تحويلات المخازن')): ?>
                     <li>
                        <a href="<?php echo e(url('StoresTransfers')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/035.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Stores_Transfers')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول تحويلات المخازن')): ?>
                     <li>
                        <a href="<?php echo e(url('StoresTransfersSechdule')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/036.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Stores_Transfers_Sechdule')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('دليل الاصناف')): ?>
                     <li>
                        <a href="<?php echo e(url('ItemsGuide')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/031.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Items_Guide')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه صنف')): ?>
                     <li>
                        <a href="<?php echo e(url('Add_Items')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/032.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Add_Items')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول الاصناف')): ?>
                     <li>
                        <a href="<?php echo e(url('Products_Sechdule')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/033.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Products_Sechdule')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('العملاء')): ?>
                     <li>
                        <a href="<?php echo e(url('Clients')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/014.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Clients')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الموردين')): ?>
                     <li>
                        <a href="<?php echo e(url('Vendors')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/007.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Vendors')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه امر شراء')): ?>
                     <li>
                        <a href="<?php echo e(url('PurchasesOrder')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/008.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.PurchasesOrder')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول آوامر الشراء')): ?>
                     <li>
                        <a href="<?php echo e(url('PurchasesOrderSechdule')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/009.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.PurchasesOrderSechdule')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه مشتريات')): ?>
                     <li>
                        <a href="<?php echo e(url('Purchases')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/010.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Purchases')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول المشتريات')): ?>
                     <li>
                        <a href="<?php echo e(url('PurchasesSechdule')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/011.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.PurchasesSechdule')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الفواتير المعلقه مشتريات')): ?>
                     <li>
                        <a href="<?php echo e(url('PurchasesHold')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/012.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.PurchasesHold')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه عرض سعر')): ?>
                     <li>
                        <a href="<?php echo e(url('Quote')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/015.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Quote')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول عرض سعر')): ?>
                     <li>
                        <a href="<?php echo e(url('Quote_Sechdule')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/016.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Quote_Sechdule')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه امر بيع')): ?>
                     <li>
                        <a href="<?php echo e(url('PurchasesOrderSechdule')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/017.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.SalesOrder')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جدول امر البيع')): ?>
                     <li>
                        <a href="<?php echo e(url('SalesOrderSechdule')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/018.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.SalesOrderSechdule')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الفواتير المعلقه مبيعات')): ?>
                     <li>
                        <a href="<?php echo e(url('SalesHoldSechdule')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/021.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.SalesHoldSechdule')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه موظف')): ?>
                     <li>
                        <a href="<?php echo e(url('AddEmp')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/022.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Add_Emp')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('صرف راتب')): ?>
                     <li>
                        <a href="<?php echo e(url('AddSalary')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/023.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.AddSalary')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('استعلام عن منتج')): ?>
                     <li>
                        <a href="<?php echo e(url('Product_Info')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/024.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Product_Info')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ميزان المراجعه')): ?>
                     <li>
                        <a href="<?php echo e(url('Trial_Balance')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/025.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Trial_Balance')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('كشف حساب خزنه، بنك')): ?>
                     <li>
                        <a href="<?php echo e(url('Safe_Bank_Statement')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/026.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Safe_Bank_Statement')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('كشف حساب موردين')): ?>
                     <li>
                        <a href="<?php echo e(url('Vendor_Account_Statement')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/027.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Vendor_Account_Statement')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('كشف حساب عملاء')): ?>
                     <li>
                        <a href="<?php echo e(url('Customer_Account_Statement')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/028.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Customer_Account_Statement')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('قائمه الدخل')): ?>
                     <li>
                        <a href="<?php echo e(url('Incom_List')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/029.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Incom_List')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('قائمه المركز المالي')): ?>
                     <li>
                        <a href="<?php echo e(url('Financial_Center')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/030.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Financial_Center')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('طباعه الباركود')): ?>
                     <li>
                        <a href="<?php echo e(url('BarcodeـPrinting')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/034.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.BarcodeـPrinting')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الدليل المحاسبي')): ?>
                     <li>
                        <a href="<?php echo e(url('AccountingManual')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/001.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Accounting_Manual')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه قيد يومي')): ?>
                     <li>
                        <a href="<?php echo e(url('Journalizing')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/002.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Journalizing')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('نقاط البيع')): ?>
                     <?php if(auth()->guard('admin')->user()->emp != 0): ?>
                     <li>
                        <a href="<?php echo e(url('POS')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/002.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.POS')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('جرد المخازن')): ?>
                     <li>
                        <a href="<?php echo e(url('StoresInventory')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/002.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.StoresInventory')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تحصيل مناديب')): ?>
                     <li>
                        <a href="<?php echo e(url('Collection_Delegates')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/002.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Collection_Delegates')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('مبيعات مناديب')): ?>
                     <li>
                        <a href="<?php echo e(url('Sales_Delegates')); ?>" class="app-list-item hover-white">
                        <span class="icon-stack">
                        <img style="width:50px;height:50px" src='<?php echo e(asset("Admin/assets/images/002.gif")); ?>'>
                        </span>
                        <span class="app-list-name">
                        <?php echo e(trans('admin.Sales_Delegates')); ?>

                        </span>
                        </a>
                     </li>
                     <?php endif; ?>
                  </ul>
                  <?php endif; ?>
               </div>
            </div>
         </div>
        <?php endif; ?>
         <!-- app message -->




             <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('الاشعارات')): ?>
         <!-- app notification -->
            <div>
               <a href="#" class="header-icon" data-toggle="dropdown" title="You got 11 notifications">
               <i class="fal fa-bell"></i>
               <span class="badge badge-icon"><?php echo e($NotificationsCount); ?></span>
               </a>
               <div class="dropdown-menu dropdown-menu-animated dropdown-xl">
                  <div class="dropdown-header bg-trans-gradient d-flex justify-content-center align-items-center rounded-top mb-2">
                     <h4 class="m-0 text-center color-white">
                        <?php echo e($NotificationsCount); ?>

                        <small class="mb-0 opacity-80"><?php echo e(trans('admin.New_Notifications')); ?></small>
                     </h4>
                  </div>

                  <div class="tab-content tab-notification">

                     <div class="tab-pane active" id="tab-messages" role="tabpanel">
                        <div class="custom-scroll h-100">
                           <ul class="notification">


                            <?php $__currentLoopData = $Notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $noti): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                              <li class="unread">
                                 <a href="#" class="d-flex align-items-center">
                                 <span class="status mr-2">
                                 <span class="profile-image rounded-circle d-inline-block" style="background-image:url(<?php echo e(asset('Admin/img/demo/avatars/avatar-c.png')); ?>)"></span>
                                 </span>
                                 <span class="d-flex flex-column flex-1 ml-1">
                                 <span class="name"><?php echo e(app()->getLocale() == 'ar' ?$noti->Type :$noti->TypeEn); ?> (<?php echo e($noti->Type_Code); ?>)</span>
                                 <span class="msg-a fs-sm"><?php echo e(app()->getLocale() == 'ar' ?$noti->Noti_Ar_Name :$noti->Noti_En_Name); ?></span>
                                 <span class="fs-nano text-muted mt-1"><?php echo e($noti->Date); ?></span>
                                 </span>
                                 </a>
                              </li>
                               <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



                           </ul>
                        </div>
                     </div>

                  </div>
                  <div class="py-2 px-3 bg-faded d-block rounded-bottom text-right border-faded border-bottom-0 border-right-0 border-left-0">
                     <a href="<?php echo e(url('AllNotifucations')); ?>" class="fs-xs fw-500 ml-auto"><?php echo e(trans('admin.view_all_notifications')); ?> </a>
                  </div>
               </div>
            </div>
               <?php endif; ?>



         <!-- app user menu -->
         <div>
            <a href="#" data-toggle="dropdown" title="<EMAIL>" class="header-icon d-flex align-items-center justify-content-center ml-2">
            <?php if(!empty(auth()->guard('admin')->user()->image)): ?>
            <img src="<?php echo e(URL::to(auth()->guard('admin')->user()->image)); ?>" class="profile-image rounded-circle" alt="<?php echo e(auth()->guard('admin')->user()->name); ?>">
            <?php else: ?>
            <img src="<?php echo e(asset('Admin/img/default.jpeg')); ?>" class="profile-image rounded-circle" alt="<?php echo e(asset('Admin/img/default.jpeg')); ?>">
            <?php endif; ?>
            </a>
            <div class="dropdown-menu dropdown-menu-animated dropdown-lg">
               <div class="dropdown-header bg-trans-gradient d-flex flex-row py-4 rounded-top">
                  <div class="d-flex flex-row align-items-center mt-1 mb-1 color-white">
                     <span class="mr-2">
                     <img src="<?php echo e(URL::to(auth()->guard('admin')->user()->image)); ?>" class="rounded-circle profile-image" alt="<?php echo e(auth()->guard('admin')->user()->name); ?>">
                     </span>
                     <div class="info-card-text">
             <div class="fs-lg text-truncate text-truncate-lg"> <?php echo e(app()->getLocale() == 'ar' ?auth()->guard('admin')->user()->name :auth()->guard('admin')->user()->nameEn); ?>   </div>
                        <span class="text-truncate text-truncate-md opacity-80"><?php echo e(auth()->guard('admin')->user()->email); ?></span>
                     </div>
                  </div>
               </div>
               <div class="dropdown-divider m-0"></div>
               <a href="#" class="dropdown-item" data-action="app-reset">
               <span data-i18n="drpdwn.reset_layout">Reset Layout</span>
               </a>
               <a href="#" class="dropdown-item" data-toggle="modal" data-target=".js-modal-settings">
               <span data-i18n="drpdwn.settings">Settings</span>
               </a>
               <div class="dropdown-divider m-0"></div>
               <a href="#" class="dropdown-item" data-action="app-fullscreen">
               <span data-i18n="drpdwn.fullscreen">Fullscreen</span>
               <i class="float-right text-muted fw-n">F11</i>
               </a>
               <a href="#" class="dropdown-item" data-action="app-print">
               <span data-i18n="drpdwn.print">Print</span>
               <i class="float-right text-muted fw-n">Ctrl + P</i>
               </a>
               <a href="<?php echo e(url('Profile')); ?>" class="dropdown-item">
               <span data-i18n="drpdwn.print">  <?php echo e(trans('admin.Profile')); ?> </span>
               </a>
               <div class="dropdown-multilevel dropdown-multilevel-left">
                  <div class="dropdown-item">
                     <?php echo e(trans('admin.Language')); ?>

                  </div>
                  <div class="dropdown-menu">
                     <a href="<?php echo e(url('lang/ar')); ?>" class="dropdown-item"><?php echo e(trans('admin.Arabic')); ?></a>
                     <a href="<?php echo e(url('lang/en')); ?>" class="dropdown-item"><?php echo e(trans('admin.English')); ?></a>
                  </div>
               </div>
               <div class="dropdown-divider m-0"></div>
               <a class="dropdown-item fw-500 pt-3 pb-3" href="<?php echo e(url('Logout')); ?>">
               <span data-i18n="drpdwn.page-logout"><?php echo e(trans('admin.Logout')); ?></span>
               <span class="float-right fw-n">&commat;OST Apps</span>
               </a>
            </div>
         </div>
      </div>
   </header>





   <!-- END Page Header -->
<?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/admin/layouts/navbar.blade.php ENDPATH**/ ?>