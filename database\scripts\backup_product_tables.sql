-- Product Tables Backup Script
-- This script creates backup tables for all product-related data
-- Run this BEFORE cleaning to create backups

-- Create backup tables with current timestamp suffix
SET @backup_suffix = CONCAT('_backup_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'));

-- Backup main products table
SET @sql = CONCAT('CREATE TABLE products', @backup_suffix, ' AS SELECT * FROM products');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Backup related tables
SET @sql = CONCAT('CREATE TABLE assembly_products', @backup_suffix, ' AS SELECT * FROM assembly_products');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE fifo_qties', @backup_suffix, ' AS SELECT * FROM fifo_qties');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE product_moves', @backup_suffix, ' AS SELECT * FROM product_moves');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE product_moves_filters', @backup_suffix, ' AS SELECT * FROM product_moves_filters');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE product_moves_filter_twos', @backup_suffix, ' AS SELECT * FROM product_moves_filter_twos');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE product_units', @backup_suffix, ' AS SELECT * FROM product_units');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE products_purchases', @backup_suffix, ' AS SELECT * FROM products_purchases');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE products_qties', @backup_suffix, ' AS SELECT * FROM products_qties');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE products_start_periods', @backup_suffix, ' AS SELECT * FROM products_start_periods');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE products_stores', @backup_suffix, ' AS SELECT * FROM products_stores');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE sub_images', @backup_suffix, ' AS SELECT * FROM sub_images');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE ticket_products', @backup_suffix, ' AS SELECT * FROM ticket_products');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Show backup tables created
SELECT CONCAT('Backup completed! Tables created with suffix: ', @backup_suffix) as message;

-- List all backup tables
SELECT TABLE_NAME as backup_tables_created 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE CONCAT('%', @backup_suffix)
ORDER BY TABLE_NAME;
