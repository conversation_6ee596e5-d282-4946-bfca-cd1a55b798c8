-- Product Tables Cleanup Script
-- This script will clean all product-related tables
-- Run this script to remove all product data from the database

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- Clean child tables first (tables that reference products)
TRUNCATE TABLE `assembly_products`;
TRUNCATE TABLE `fifo_qties`;
TRUNCATE TABLE `product_moves`;
TRUNCATE TABLE `product_moves_filters`;
TRUNCATE TABLE `product_moves_filter_twos`;
TRUNCATE TABLE `product_units`;
TRUNCATE TABLE `products_purchases`;
TRUNCATE TABLE `products_qties`;
TRUNCATE TABLE `products_start_periods`;
TRUNCATE TABLE `products_stores`;
TRUNCATE TABLE `sub_images`;
TRUNCATE TABLE `ticket_products`;

-- Clean main products table last
TRUNCATE TABLE `products`;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Reset auto-increment counters (optional)
ALTER TABLE `products` AUTO_INCREMENT = 1;
ALTER TABLE `assembly_products` AUTO_INCREMENT = 1;
ALTER TABLE `fifo_qties` AUTO_INCREMENT = 1;
ALTER TABLE `product_moves` AUTO_INCREMENT = 1;
ALTER TABLE `product_moves_filters` AUTO_INCREMENT = 1;
ALTER TABLE `product_moves_filter_twos` AUTO_INCREMENT = 1;
ALTER TABLE `product_units` AUTO_INCREMENT = 1;
ALTER TABLE `products_purchases` AUTO_INCREMENT = 1;
ALTER TABLE `products_qties` AUTO_INCREMENT = 1;
ALTER TABLE `products_start_periods` AUTO_INCREMENT = 1;
ALTER TABLE `products_stores` AUTO_INCREMENT = 1;
ALTER TABLE `sub_images` AUTO_INCREMENT = 1;
ALTER TABLE `ticket_products` AUTO_INCREMENT = 1;

-- Verification queries (optional - uncomment to check)
-- SELECT 'products' as table_name, COUNT(*) as record_count FROM products
-- UNION ALL
-- SELECT 'assembly_products', COUNT(*) FROM assembly_products
-- UNION ALL
-- SELECT 'fifo_qties', COUNT(*) FROM fifo_qties
-- UNION ALL
-- SELECT 'product_moves', COUNT(*) FROM product_moves
-- UNION ALL
-- SELECT 'product_moves_filters', COUNT(*) FROM product_moves_filters
-- UNION ALL
-- SELECT 'product_moves_filter_twos', COUNT(*) FROM product_moves_filter_twos
-- UNION ALL
-- SELECT 'product_units', COUNT(*) FROM product_units
-- UNION ALL
-- SELECT 'products_purchases', COUNT(*) FROM products_purchases
-- UNION ALL
-- SELECT 'products_qties', COUNT(*) FROM products_qties
-- UNION ALL
-- SELECT 'products_start_periods', COUNT(*) FROM products_start_periods
-- UNION ALL
-- SELECT 'products_stores', COUNT(*) FROM products_stores
-- UNION ALL
-- SELECT 'sub_images', COUNT(*) FROM sub_images
-- UNION ALL
-- SELECT 'ticket_products', COUNT(*) FROM ticket_products;
