/* Product Card Button Padding Fix */
/* Minimal fix for the 2 floated buttons in product cards */

/* Fix padding for the action buttons */
.product-card .actions .btn,
.product-card .actions .btn.style2 {
    padding: 8px 12px !important;
    margin: 0 5px !important;
    min-width: 40px !important;
    min-height: 40px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Ensure proper spacing between the 2 buttons */
.product-card .actions {
    gap: 10px !important;
}

/* Fix any margin issues */
.product-card .actions > * {
    margin: 0 !important;
}

/* Ensure icons are properly centered */
.product-card .actions .btn i,
.product-card .actions .btn.style2 i {
    margin: 0 !important;
    line-height: 1 !important;
}

/* Cart Page Button Fixes */
/* Fix Continue Shopping button - make text white always, not just on hover */
.continue-shopping {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
    border: 2px solid #28a745 !important;
}

.continue-shopping:hover {
    background: linear-gradient(135deg, #20c997 0%, #28a745 100%) !important;
    color: white !important;
    border-color: #20c997 !important;
    text-decoration: none !important;
}

/* Quantity Controls Fix */
/* Fix quantity input visibility and button functionality */
.quantity-controls {
    display: flex !important;
    align-items: center !important;
    border: 2px solid #ddd !important;
    border-radius: 10px !important;
    overflow: hidden !important;
    width: 130px !important;
    background: white !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.qty-input {
    border: none !important;
    width: 60px !important;
    height: 45px !important;
    text-align: center !important;
    font-weight: 700 !important;
    background: white !important;
    color: #000 !important;
    outline: none !important;
    font-size: 16px !important;
    -webkit-appearance: none !important;
    -moz-appearance: textfield !important;
    line-height: 1 !important;
    padding: 0 !important;
    margin: 0 !important;
}

.qty-input::-webkit-outer-spin-button,
.qty-input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
}

.qty-btn {
    background: #f8f9fa !important;
    border: none !important;
    width: 35px !important;
    height: 45px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    color: #333 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    position: relative !important;
}

.qty-btn:hover {
    background: #28a745 !important;
    color: white !important;
    transform: scale(1.05) !important;
}

.qty-btn:active {
    transform: scale(0.95) !important;
    background: #1e7e34 !important;
}

.qty-btn.minus {
    border-right: 1px solid #ddd !important;
    border-radius: 10px 0 0 10px !important;
}

.qty-btn.plus {
    border-left: 1px solid #ddd !important;
    border-radius: 0 10px 10px 0 !important;
}

.qty-btn i {
    font-size: 14px !important;
    line-height: 1 !important;
}

/* Ensure quantity input is always visible and black text */
.qty-input:focus {
    border: none !important;
    outline: 2px solid #28a745 !important;
    outline-offset: -2px !important;
    background: white !important;
    color: #000 !important;
}

/* Additional specificity for black text */
input.qty-input,
input[name="qty[]"] {
    color: #000 !important;
    background: white !important;
    font-weight: 700 !important;
}

/* Override any conflicting styles */
.cart-area .qty-input,
.cart-wrapper .qty-input,
.item-quantity .qty-input {
    color: #000 !important;
    background: white !important;
    font-weight: 700 !important;
    font-size: 16px !important;
}

/* Enhanced button styling for better visibility */
.qty-btn.minus:before {
    content: "−" !important;
    font-size: 18px !important;
    font-weight: 700 !important;
    line-height: 1 !important;
}

.qty-btn.plus:before {
    content: "+" !important;
    font-size: 16px !important;
    font-weight: 700 !important;
    line-height: 1 !important;
}

.qty-btn i {
    display: none !important;
}
